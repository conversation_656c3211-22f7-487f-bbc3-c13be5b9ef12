var moment = require('moment')

module.exports = function (db, Sequelize) {

    // Define entity models

    var CampaignType = db.define('campaigntype', {
        name: Sequelize.STRING,
        description: Sequelize.STRING
    })


    var BroadcastMessage = db.define('broadcastmessage', {
        title: Sequelize.STRING,
        content: Sequelize.STRING,
        uniqueId: Sequelize.STRING
    })


    var CampaignTrainingDoc = db.define('campaigntrainingdoc', {
        name: Sequelize.STRING,
        description: Sequelize.STRING,
        path: Sequelize.STRING,
        link: Sequelize.TEXT
    })


    var Skill = db.define('skill', {
        name: Sequelize.STRING,
        description: Sequelize.STRING
    })


    var SubSkill = db.define('subskill', {
        name: Sequelize.STRING,
        priority: Sequelize.INTEGER,
        description: Sequelize.STRING
    })


    var Disposition = db.define('disposition', {
        name: Sequelize.STRING,
        description: Sequelize.STRING,
        exhaustLead: Sequelize.BOOLEAN,
        validateLead: Sequelize.BOOLEAN,
        system: Sequelize.BOOLEAN,
        controller: Sequelize.STRING,
        templateUrl: Sequelize.STRING,
        size: Sequelize.STRING
    })


    var DateTimeRuleSet = db.define('datetimeruleset', {
        name: Sequelize.STRING,
        description: Sequelize.STRING,
        startTime: Sequelize.TIME,
        endTime: Sequelize.TIME,
        monday: Sequelize.BOOLEAN,
        tuesday: Sequelize.BOOLEAN,
        wednesday: Sequelize.BOOLEAN,
        thursday: Sequelize.BOOLEAN,
        friday: Sequelize.BOOLEAN,
        saturday: Sequelize.BOOLEAN,
        sunday: Sequelize.BOOLEAN
    })


    var CampaignStageDateTimeRule = db.define('campaignstagedatetimerules', {
        quantity: Sequelize.INTEGER,
        startDate: Sequelize.DATE,
        endDate: Sequelize.DATE,
        uuid: Sequelize.CHAR(36)
    })

    var LeadUpdate = db.define('leadupdate', {
        type: Sequelize.STRING,
        from: Sequelize.STRING,
        to: Sequelize.STRING,
        data: Sequelize.TEXT
    })

    var CampaignLeadUpdate = db.define('campaignleadupdate', {
        from: Sequelize.STRING,
        to: Sequelize.STRING,
        data: Sequelize.TEXT
    })

    var Lead = db.define('lead', {
        dontContactUntil: Sequelize.DATE,
        clientSourceCode: Sequelize.STRING,
        salutation: Sequelize.STRING,
        first_name: Sequelize.STRING,
        last_name: Sequelize.STRING,
        suffix: Sequelize.STRING,
        spouse_name: Sequelize.STRING,
        company_name: Sequelize.STRING,
        address1: Sequelize.STRING,
        address2: Sequelize.STRING,
        address3: Sequelize.STRING,
        city: Sequelize.STRING,
        state: Sequelize.STRING,
        zip: Sequelize.STRING,
        phone_home: Sequelize.STRING,
        phone_mobile: Sequelize.STRING,
        phone_work: Sequelize.STRING,
        phone_workmobile: Sequelize.STRING,
        email: Sequelize.STRING,
        clientRef: Sequelize.STRING,
        division: Sequelize.STRING,
        gift1Date: Sequelize.TEXT,
        gift1Amount: Sequelize.TEXT,
        gift1Source: Sequelize.TEXT,
        gift1Ref: Sequelize.TEXT,
        gift1Benefits: Sequelize.TEXT,
        gift2Date: Sequelize.TEXT,
        gift2Amount: Sequelize.TEXT,
        gift2Source: Sequelize.TEXT,
        gift2Ref: Sequelize.TEXT,
        gift2Benefits: Sequelize.TEXT,
        gift3Date: Sequelize.TEXT,
        gift3Amount: Sequelize.TEXT,
        gift3Source: Sequelize.TEXT,
        gift3Ref: Sequelize.TEXT,
        gift3Benefits: Sequelize.TEXT,
        gift4Date: Sequelize.TEXT,
        gift4Amount: Sequelize.TEXT,
        gift4Source: Sequelize.TEXT,
        gift4Ref: Sequelize.TEXT,
        gift4Benefits: Sequelize.TEXT,
        gift5Date: Sequelize.TEXT,
        gift5Amount: Sequelize.TEXT,
        gift5Source: Sequelize.TEXT,
        gift5Ref: Sequelize.TEXT,
        gift5Benefits: Sequelize.TEXT,
        lyAmount: Sequelize.TEXT,
        lyMatchAmount: Sequelize.TEXT,
        lyPledgeDate: Sequelize.TEXT,
        lap1Amount: Sequelize.TEXT,
        lap1MatchAmount: Sequelize.TEXT,
        lap2Amount: Sequelize.TEXT,
        lap2MatchAmount: Sequelize.TEXT,
        lap3Amount: Sequelize.TEXT,
        lap3MatchAmount: Sequelize.TEXT,
        lap4Amount: Sequelize.TEXT,
        lap4MatchAmount: Sequelize.TEXT,
        lifttimeGiving: Sequelize.TEXT,
        lifetimeGivingDate: Sequelize.TEXT,
        lifetimePledges: Sequelize.TEXT,
        lifetimeBuying: Sequelize.TEXT,
        lifetimeBuyingDate: Sequelize.TEXT,
        tix1Type: Sequelize.TEXT,
        tix1Yr: Sequelize.TEXT,
        tix1Event: Sequelize.TEXT,
        tix1Date: Sequelize.TEXT,
        tix1Cost: Sequelize.TEXT,
        tix1Loc: Sequelize.TEXT,
        tix1Seats: Sequelize.TEXT,
        tix1Addl: Sequelize.TEXT,
        tix2Type: Sequelize.TEXT,
        tix2Yr: Sequelize.TEXT,
        tix2Event: Sequelize.TEXT,
        tix2Date: Sequelize.TEXT,
        tix2Cost: Sequelize.TEXT,
        tix2Loc: Sequelize.TEXT,
        tix2Seats: Sequelize.TEXT,
        tix2Addl: Sequelize.TEXT,
        tix3Type: Sequelize.TEXT,
        tix3Yr: Sequelize.TEXT,
        tix3Event: Sequelize.TEXT,
        tix3Date: Sequelize.TEXT,
        tix3Cost: Sequelize.TEXT,
        tix3Loc: Sequelize.TEXT,
        tix3Seats: Sequelize.TEXT,
        tix3Addl: Sequelize.TEXT,
        tix4Type: Sequelize.TEXT,
        tix4Yr: Sequelize.TEXT,
        tix4Event: Sequelize.TEXT,
        tix4Date: Sequelize.TEXT,
        tix4Cost: Sequelize.TEXT,
        tix4Loc: Sequelize.TEXT,
        tix4Seats: Sequelize.TEXT,
        tix4Addl: Sequelize.TEXT,
        tix5Type: Sequelize.TEXT,
        tix5Yr: Sequelize.TEXT,
        tix5Event: Sequelize.TEXT,
        tix5Date: Sequelize.TEXT,
        tix5Cost: Sequelize.TEXT,
        tix5Loc: Sequelize.TEXT,
        tix5Seats: Sequelize.TEXT,
        tix5Addl: Sequelize.TEXT,
        tix6Type: Sequelize.TEXT,
        tix6Yr: Sequelize.TEXT,
        tix6Event: Sequelize.TEXT,
        tix6Date: Sequelize.TEXT,
        tix6Cost: Sequelize.TEXT,
        tix6Loc: Sequelize.TEXT,
        tix6Seats: Sequelize.TEXT,
        tix6Addl: Sequelize.TEXT,
        tix7Type: Sequelize.TEXT,
        tix7Yr: Sequelize.TEXT,
        tix7Event: Sequelize.TEXT,
        tix7Date: Sequelize.TEXT,
        tix7Cost: Sequelize.TEXT,
        tix7Loc: Sequelize.TEXT,
        tix7Seats: Sequelize.TEXT,
        tix7Addl: Sequelize.TEXT,
        tix8Type: Sequelize.TEXT,
        tix8Yr: Sequelize.TEXT,
        tix8Event: Sequelize.TEXT,
        tix8Date: Sequelize.TEXT,
        tix8Cost: Sequelize.TEXT,
        tix8Loc: Sequelize.TEXT,
        tix8Seats: Sequelize.TEXT,
        tix8Addl: Sequelize.TEXT,
        tix9Type: Sequelize.TEXT,
        tix9Yr: Sequelize.TEXT,
        tix9Event: Sequelize.TEXT,
        tix9Date: Sequelize.TEXT,
        tix9Cost: Sequelize.TEXT,
        tix9Loc: Sequelize.TEXT,
        tix9Seats: Sequelize.TEXT,
        tix9Addl: Sequelize.TEXT,
        lastGiftAmount: Sequelize.STRING,
        lastGiftDate: Sequelize.STRING,
        lastGiftReference: Sequelize.STRING,
        lastGiftSource: Sequelize.STRING,
        lastGiftCC: Sequelize.STRING,
        tix1: Sequelize.STRING,
        tix2: Sequelize.STRING,
        tix3: Sequelize.STRING,
        tix4: Sequelize.STRING,
        tix5: Sequelize.STRING,
        tix6: Sequelize.STRING,
        tix7: Sequelize.STRING,
        tix8: Sequelize.STRING,
        existingCCType: Sequelize.STRING,
        existingCCDigits: Sequelize.STRING,
        existingCCExp: Sequelize.STRING,
        tixClientRef: Sequelize.STRING,
        notes: Sequelize.STRING(250),
        reimported: Sequelize.BOOLEAN,
        benefits: Sequelize.STRING,
        clientWorker: Sequelize.STRING,
        memberLevel: Sequelize.STRING,
        custom1: Sequelize.STRING,
        custom2: Sequelize.STRING,
        interaction1Date: Sequelize.TEXT,
        interaction1Type: Sequelize.TEXT,
        interaction1Detail: Sequelize.TEXT,
        interaction2Date: Sequelize.TEXT,
        interaction2Type: Sequelize.TEXT,
        interaction2Detail: Sequelize.TEXT,
        interaction3Date: Sequelize.TEXT,
        interaction3Type: Sequelize.TEXT,
        interaction3Detail: Sequelize.TEXT,
        interaction4Date: Sequelize.TEXT,
        interaction4Type: Sequelize.TEXT,
        interaction4Detail: Sequelize.TEXT,
        interaction5Date: Sequelize.TEXT,
        interaction5Type: Sequelize.TEXT,
        interaction5Detail: Sequelize.TEXT,
        interaction6Date: Sequelize.TEXT,
        interaction6Type: Sequelize.TEXT,
        interaction6Detail: Sequelize.TEXT,
        interaction7Date: Sequelize.TEXT,
        interaction7Type: Sequelize.TEXT,
        interaction7Detail: Sequelize.TEXT,
        interaction8Date: Sequelize.TEXT,
        interaction8Type: Sequelize.TEXT,
        interaction8Detail: Sequelize.TEXT,
        monthlyGivingAmount: Sequelize.TEXT,
        monthlyGivingStart: Sequelize.TEXT,
        monthlyGivingEnd: Sequelize.TEXT,
        customFields: Sequelize.TEXT,
        lastAgent: Sequelize.INTEGER,
        tyAmount: Sequelize.TEXT,
        agentPortfolioTag: Sequelize.TEXT
    })

    var LeadField = db.define('leadfield', {
        name: Sequelize.STRING,
        type: Sequelize.STRING,
        regex: Sequelize.STRING
    })


    var LeadFieldValue = db.define('leadfieldvalue', {
        value: Sequelize.STRING
    })


    var CampaignLead = db.define('campaignleads', {
        campaignStageHistory: Sequelize.STRING,
        skill: Sequelize.STRING,
        subSkill: Sequelize.STRING,
        transitionDate: Sequelize.DATE,
        currentCampaignStageId: Sequelize.INTEGER
    })


    var CallAttempt = db.define('callattempt', {
        startTime: Sequelize.TIME,
        endTime: Sequelize.TIME,
        startDate: Sequelize.DATE,
        endDate: Sequelize.DATE,
        monday: Sequelize.BOOLEAN,
        tuesday: Sequelize.BOOLEAN,
        wednesday: Sequelize.BOOLEAN,
        thursday: Sequelize.BOOLEAN,
        friday: Sequelize.BOOLEAN,
        saturday: Sequelize.BOOLEAN,
        sunday: Sequelize.BOOLEAN,
        isCallback: Sequelize.BOOLEAN,
        createdFromDTUuid: Sequelize.CHAR(36),
        randomSelector: Sequelize.CHAR(36),
        lastDispositionDate: Sequelize.DATE
    })


    var CallResult = db.define('callresult', {
        wrapup: Sequelize.STRING,
        wrapupduration: Sequelize.INTEGER,
        completed: Sequelize.BOOLEAN,
        notes: Sequelize.TEXT,
        callAttemptJson: Sequelize.TEXT,
        giftAmount: Sequelize.FLOAT,
        freeTickets: Sequelize.INTEGER,
        paymentType: Sequelize.STRING,
        creditCardType: Sequelize.STRING,
        creditCardNumber: Sequelize.STRING,
        creditCardExpDate: Sequelize.STRING,
        creditCardSecurityCode: Sequelize.STRING,
        installmentNotes: Sequelize.STRING,
        numberOfInstallments: Sequelize.INTEGER,
        requiresFollowUp: Sequelize.BOOLEAN,
        payDate: Sequelize.STRING,
        decisionMaker: Sequelize.STRING,
        giftMatchingCompany: Sequelize.STRING,
        declineBenefits: Sequelize.BOOLEAN,
        invoiceMethod: Sequelize.STRING,
        refusalReason: Sequelize.STRING,
        saleAmount: Sequelize.DOUBLE,
        grandTotal: Sequelize.DOUBLE,
        skill: Sequelize.STRING,
        subSkill: Sequelize.STRING,
        payAmount: Sequelize.DOUBLE,
        newMembershipCard: Sequelize.BOOLEAN,
        useExistingCreditCard: Sequelize.BOOLEAN
    })


    var CallRecord = db.define('callrecord', {
        startDateTime: Sequelize.DATE,
        connectedDateTime: Sequelize.DATE,
        endDateTime: Sequelize.DATE,
        connectedDurationSecs: Sequelize.INTEGER,
        ringDurationSecs: Sequelize.INTEGER,
        totalDurationSecs: Sequelize.INTEGER,
        callTo: Sequelize.STRING,
        callFrom: Sequelize.STRING,
        callerId: Sequelize.STRING,
        callFromType: Sequelize.STRING,
        callToType: Sequelize.STRING,
        phoneType: Sequelize.STRING,
        direction: Sequelize.STRING,
        callUuid: Sequelize.CHAR(36),
        recorded: Sequelize.BOOLEAN,
        recordingLocation: Sequelize.STRING,
        recordingServer: Sequelize.STRING
    })


    var Callback = db.define('callback', {
        startDateTime: Sequelize.DATE,
        endDateTime: Sequelize.DATE,
        callAttemptJson: Sequelize.TEXT,
        campaignId: Sequelize.INTEGER,
        phone: Sequelize.STRING,
        deleted: Sequelize.BOOLEAN,
        expired: Sequelize.BOOLEAN
    })


    var Campaign = db.define('campaign', {
        name: Sequelize.STRING,
        defaultCallerId: Sequelize.STRING,
        startDate: Sequelize.DATE,
        endDate: Sequelize.DATE,
        goal: Sequelize.INTEGER,
        acquisitionGoal: Sequelize.INTEGER,
        renewalGoal: Sequelize.INTEGER,
        lapsedGoal: Sequelize.INTEGER,
        addonGoal: Sequelize.INTEGER,
        initialCampaignStageId: Sequelize.INTEGER,
        secondAppealProjectedQuantity: Sequelize.INTEGER,
        secondAppealProjectedAvgValue: Sequelize.DOUBLE,
        callbackWindow: Sequelize.INTEGER,
        dontRecord: Sequelize.BOOLEAN
    })


    var CampaignUpdate = db.define('campaignupdate', {
        field: Sequelize.STRING,
        from: Sequelize.STRING,
        to: Sequelize.STRING,
        detail: Sequelize.STRING
    })


    var CampaignStage = db.define('campaignstage', {
        name: Sequelize.STRING,
        startDate: Sequelize.DATE,
        endDate: Sequelize.DATE,
        callerId: Sequelize.STRING,
        blacklistedSkills: Sequelize.TEXT,
        isSystem: Sequelize.BOOLEAN,
        blacklistCampaignstageId: Sequelize.INTEGER
    })


    var Client = db.define('client', {
        name: Sequelize.STRING,
        address1: Sequelize.STRING,
        address2: Sequelize.STRING,
        city: Sequelize.STRING,
        state: Sequelize.STRING,
        zip: Sequelize.STRING,
        primaryContactName: Sequelize.STRING,
        primaryContactNumber: Sequelize.STRING,
        secondaryContactName: Sequelize.STRING,
        secondaryContactNumber: Sequelize.STRING,
        email: Sequelize.STRING,
        timezone: Sequelize.STRING,
        additionalInfo: Sequelize.TEXT,
        defaultInvoiceType: Sequelize.STRING,
        salesTax: Sequelize.DOUBLE,
        orderFee: Sequelize.DOUBLE,
        letterSalutation: Sequelize.STRING,
        contactTitle: Sequelize.STRING,
        website: Sequelize.STRING,
        returnEmail: Sequelize.STRING,
        invoiceMessage: Sequelize.TEXT,
        firstAppealInvoiceText: Sequelize.TEXT,
        secondAppealInvoiceText: Sequelize.TEXT,
        followUpInvoiceText: Sequelize.TEXT,
        UpafFirstAppealInvoiceText: Sequelize.TEXT,
        UpafSecondAppealInvoiceText: Sequelize.TEXT,
        UpafFollowUpInvoiceText: Sequelize.TEXT,
        signature: Sequelize.STRING,
        allowExistingCC: Sequelize.BOOLEAN,
        logo: Sequelize.STRING,
        membershipCard: Sequelize.BOOLEAN,
        showReportsAsViews: Sequelize.BOOLEAN,
        reportPasswordRequired: { type: Sequelize.BOOLEAN, defaultValue: false },
        reportPassword: Sequelize.STRING,
        reportPasswordLastChange: Sequelize.DATE,
        directPayments: { type: Sequelize.BOOLEAN, defaultValue: false },
        paperInvoices: { type: Sequelize.BOOLEAN, defaultValue: true },
        emailInvoices: { type: Sequelize.BOOLEAN, defaultValue: true },
        invoiceSubject: Sequelize.STRING
    })


    var CampaignProduct = db.define('campaignproduct', {
        productCode: Sequelize.STRING,
        venue: Sequelize.STRING,
        series: Sequelize.STRING,
        price: Sequelize.DOUBLE,
        seats: Sequelize.STRING,
        days: Sequelize.STRING,
        feePerTicket: Sequelize.FLOAT,
        tix_sub: Sequelize.STRING,
        seriesActive: Sequelize.BOOLEAN,
        seatsActive: Sequelize.BOOLEAN,
        daysActive: Sequelize.BOOLEAN,
        order_by: Sequelize.INTEGER
    })


    var Agent = db.define('agent', {
        name: Sequelize.STRING,
        lastSessionEnd: Sequelize.DATE,
        autoDial: Sequelize.BOOLEAN,
        hourlyRate: Sequelize.DOUBLE,
        callPrepTime: Sequelize.INTEGER,
        hireDate: Sequelize.DATE,
        termDate: Sequelize.DATE,
        address: Sequelize.STRING,
        city: Sequelize.STRING,
        state: Sequelize.STRING,
        zip: Sequelize.STRING,
        DOB: Sequelize.STRING,
        wageType: Sequelize.STRING,
        phone: Sequelize.STRING,
        email: Sequelize.STRING,
        campaignSwitchInterval: Sequelize.INTEGER,
        location: Sequelize.STRING,
        ssn: Sequelize.STRING,
        deductions: Sequelize.STRING,
        wageType: Sequelize.STRING,
        otherHourlyRate: Sequelize.DOUBLE,
        healthDeduction: Sequelize.DOUBLE,
        dentalDeduction: Sequelize.DOUBLE,
        garnishment: Sequelize.DOUBLE,
        egDeduction: Sequelize.DOUBLE,
        transitDeduction: Sequelize.DOUBLE,
        payrollPaymentType: Sequelize.STRING,
        timeoutDuration: Sequelize.INTEGER,
        payrollId: Sequelize.STRING
    })


    var AgentState = db.define('agentstate', {
        name: Sequelize.STRING,
        description: Sequelize.STRING,
        outbound: Sequelize.BOOLEAN,
        color: Sequelize.STRING,
        system: Sequelize.BOOLEAN,
        isChargeable: Sequelize.BOOLEAN,
        timeout: Sequelize.INTEGER
    })


    var UserEvent = db.define('userevent', {
        eventType: Sequelize.STRING,
        eventInfo: Sequelize.STRING,
        timestamp: Sequelize.DATE,
        resolved: Sequelize.BOOLEAN,
        duration: Sequelize.INTEGER
    })


    var CampaignStageAgent = db.define('campaignstageagents', {
        scheduledHours: Sequelize.INTEGER,
        goal: Sequelize.INTEGER,
        agentskills: Sequelize.TEXT
    })


    var CampaignStageDisposition = db.define('campaignstagedispositions', {
        transitionToCampaignStageId: Sequelize.INTEGER,
        transitionCutOffDate: Sequelize.DATE,
        transitionCutOffDateDispositionId: Sequelize.INTEGER,
        dontContactLeadForHours: Sequelize.INTEGER
    })

    var CampaignAgentTarget = db.define('campaignagenttarget', {
        start: Sequelize.DATE,
        end: Sequelize.DATE,
        level: Sequelize.FLOAT,
        scheduledHours: Sequelize.FLOAT,
        goal: Sequelize.DOUBLE,
        overrideGoal: Sequelize.DOUBLE,
        notes: Sequelize.TEXT
    })

    var CallResultFieldGroup = db.define('callresultfieldgroup', {
        name: Sequelize.STRING
    })


    var CallResultField = db.define('callresultfield', {
        name: Sequelize.STRING,
        placeholder: Sequelize.STRING,
        pci: Sequelize.BOOLEAN,
        initialValue: Sequelize.STRING,
        regex: Sequelize.STRING,
        regexMessage: Sequelize.STRING,
        required: Sequelize.BOOLEAN,
        defaultValue: Sequelize.STRING
    })


    var CallResultFieldOption = db.define('callresultfieldoption', {
        name: Sequelize.STRING,
        value: Sequelize.STRING
    })


    var CallResultFieldType = db.define('callresultfieldtype', {
        name: Sequelize.STRING,
        templateUrl: Sequelize.STRING,
        showRegex: Sequelize.BOOLEAN,
        showPlaceholder: Sequelize.BOOLEAN,
        showDefaultValue: Sequelize.BOOLEAN,
        multipleOptions: Sequelize.BOOLEAN,
        multipleResults: Sequelize.BOOLEAN
    })


    var CallResultFieldValue = db.define('callresultfieldvalue', {
        value: Sequelize.STRING
    })

    var LeadAudit = db.define('leadaudit', {
        field: Sequelize.STRING,
        previousValue: Sequelize.STRING,
        newValue: Sequelize.STRING,
        acceptedBy: Sequelize.INTEGER
    })

    var User = db.define('user', {
        username: Sequelize.STRING,
        password: Sequelize.STRING,
        name: Sequelize.STRING,
        homeState: Sequelize.STRING,
        showStats: Sequelize.BOOLEAN,
        isAdmin: Sequelize.BOOLEAN,
        isSupervisor: Sequelize.BOOLEAN,
        isAgent: Sequelize.BOOLEAN,
        isClient: Sequelize.BOOLEAN,
        isSuperManager: Sequelize.BOOLEAN,
        isClientAgent: Sequelize.BOOLEAN,
        isClientAdmin: Sequelize.BOOLEAN,
        avatar: Sequelize.STRING,
        email: Sequelize.STRING,
        firstLogin: Sequelize.BOOLEAN,
        passwordhistory: Sequelize.STRING,
        lastPasswordUpdate: Sequelize.DATE
    })

    var AgentEvent = db.define('agentevent', {
        eventType: Sequelize.STRING,
        eventName: Sequelize.STRING,
        additionalInfo: Sequelize.TEXT
    })

    var Sale = db.define('sale', {
        seatCount: Sequelize.INTEGER,
        productCode: Sequelize.STRING,
        series: Sequelize.STRING,
        seats: Sequelize.STRING,
        dayOfWeek: Sequelize.STRING,
        tix_sub: Sequelize.STRING,
        costEach: Sequelize.DOUBLE,
        feePerTicket: Sequelize.DOUBLE,
        subtotal: Sequelize.DOUBLE,
        salesTax: Sequelize.DOUBLE
    })

    var Report = db.define('report', {
        name: Sequelize.STRING,
        lastRunDate: Sequelize.DATE,
        folder: Sequelize.STRING,
        filters: Sequelize.TEXT,
        definition: Sequelize.TEXT,
        isSystem: Sequelize.BOOLEAN,
        passwordProtected: { type: Sequelize.BOOLEAN, defaultValue: false }
    })

    var ReportSchedule = db.define('reportschedule', {
        startDate: Sequelize.DATE,
        startTime: Sequelize.DATE,
        frequency: Sequelize.INTEGER,
        runEvery: Sequelize.STRING,
        filters: Sequelize.TEXT,
        recipients: Sequelize.STRING,
        isClientReady: Sequelize.BOOLEAN,
        lastRun: Sequelize.DATE
    })

    var ReportHistory = db.define('reporthistory', {
        path: Sequelize.STRING,
        name: Sequelize.STRING,
        filters: Sequelize.TEXT,
        isClientReady: Sequelize.BOOLEAN
    })

    var ReportHistoryAudit = db.define('reporthistoryaudit', {
        isEmailed: Sequelize.BOOLEAN,
        recipients: Sequelize.STRING,
        isDownloaded: Sequelize.BOOLEAN,
        fileFormat: Sequelize.STRING,
        error: Sequelize.TEXT
    })

    var CampaignProjections = db.define('campaignprojections', {
        clientQuantity: Sequelize.INTEGER,
        clientAvgValue: Sequelize.DOUBLE,
        projectedRR: Sequelize.INTEGER,
        projectedQuantity: Sequelize.INTEGER,
        projectedAvgValue: Sequelize.DOUBLE,
        defaultAskAmount: Sequelize.DOUBLE,
        askAmountIncrease: Sequelize.DOUBLE
    })

    var Payroll = db.define('payroll', {
        date: Sequelize.DATE,
        totalSeconds: Sequelize.INTEGER
    })

    var LoginDuration = db.define('loginduration', {
        date: Sequelize.DATE,
        totalSeconds: Sequelize.INTEGER
    })

    var Device = db.define('device', {
        type: Sequelize.STRING,
        extension: Sequelize.STRING,
        name: Sequelize.STRING,
        server: Sequelize.STRING,
        password: Sequelize.STRING,
        recordCalls: Sequelize.BOOLEAN
    })

    var System = db.define('system', {
        key: Sequelize.STRING,
        value: Sequelize.TEXT
    }, {
        tableName: 'system'
    })

    var Invoice = db.define('invoice', {
        grandTotal: Sequelize.DOUBLE,
        amountRemaining: Sequelize.DOUBLE,
        requestCount: Sequelize.INTEGER,
        invoiceType: Sequelize.STRING,
        deliveryMethod: Sequelize.STRING,
        dueDate: Sequelize.DATE,
        writtenOff: Sequelize.BOOLEAN,
        writtenOffAmount: Sequelize.DOUBLE,
        sendInvoice: Sequelize.BOOLEAN,
        notes: Sequelize.TEXT
    })

    var InvoiceNote = db.define('invoicenote', {
        note: Sequelize.TEXT
    })

    var InvoiceHistory = db.define('invoicehistory', {
        requestAmount: Sequelize.DOUBLE,
        invoiceHtml: Sequelize.TEXT
    })

    var InvoiceEvent = db.define('invoicevent', {
        changeType: Sequelize.STRING,
        field: Sequelize.STRING,
        fromValue: Sequelize.STRING,
        toValue: Sequelize.STRING
    })

    var LeadImportHistory = db.define('leadimporthistory', {
        name: Sequelize.STRING,
        status: Sequelize.STRING,
        path: Sequelize.STRING,
        taskId: Sequelize.CHAR(36),
        updateOnly: {
            type: Sequelize.BOOLEAN,
            defaultValue: false
        },
        error: Sequelize.TEXT,
        percentComplete: Sequelize.INTEGER,
        totalRecords: Sequelize.INTEGER,
        recordsIgnored: Sequelize.INTEGER,
        recordsProcessed: Sequelize.INTEGER,
        leadsUpdated: Sequelize.INTEGER,
        leadsCreated: Sequelize.INTEGER,
        callAttemptsCreated: Sequelize.INTEGER,
    })

    var ClientCosting = db.define('clientcosting', {
        date: Sequelize.DATE,
        hours: Sequelize.INTEGER,
        cost: Sequelize.DOUBLE
    })

    var CampaignNote = db.define('campaignnote', {
        notes: Sequelize.TEXT
    })

    var EmailHistory = db.define('emailhistory', {
        success: Sequelize.BOOLEAN,
        emailSettings: Sequelize.TEXT,
        mailOptions: Sequelize.TEXT,
        error: Sequelize.STRING
    })

    var CampaignGoals = db.define('campaigngoals', {
        goal: Sequelize.INTEGER,
        reportingGroup: Sequelize.STRING,
        projectedQuantity: Sequelize.INTEGER
    })

    var PanicReports = db.define('panicreports', {
        message: Sequelize.STRING,
        jsonData: Sequelize.TEXT,
        errors: Sequelize.TEXT
    })

    var RefusalReason = db.define('refusalreason', {
        name: Sequelize.STRING,
        exception: Sequelize.BOOLEAN,
        telefunding: Sequelize.BOOLEAN,
        telemarketing: Sequelize.BOOLEAN
    })

    var AuditEvent = db.define('auditevent', {
        type: Sequelize.STRING,
        from: Sequelize.TEXT,
        to: Sequelize.TEXT,
        notes: Sequelize.TEXT
    })

    var CardPayment = db.define('cardpayment', {
        amount: Sequelize.DOUBLE,
        details: Sequelize.TEXT
    })

    var PaymentLog = db.define('paymentlog', {
        amount: Sequelize.DOUBLE,
        paymentDate: Sequelize.DATE,
        isPaid: { type: Sequelize.BOOLEAN, defaultValue: false },
        actualPaymentDate: Sequelize.DATE,
        receipt: Sequelize.TEXT,
        transactionID: Sequelize.INTEGER,
        error: Sequelize.STRING,
        status: Sequelize.STRING,
        inProgress: { type: Sequelize.BOOLEAN, defaultValue: false },
        deleted: { type: Sequelize.BOOLEAN, defaultValue: false },
        disabled: { type: Sequelize.BOOLEAN, defaultValue: false },
        source: Sequelize.STRING
    })

    var PaymentLogHistory = db.define('paymentloghistory', {
        amount: Sequelize.DOUBLE,
        isPaid: { type: Sequelize.BOOLEAN, defaultValue: false },
        receipt: Sequelize.TEXT,
        transactionID: Sequelize.INTEGER,
        error: Sequelize.STRING,
        maskedCardNumber: Sequelize.STRING,
        cardType: Sequelize.STRING,
        expirationDate: Sequelize.STRING
    })

    var CardToken = db.define('cardtoken', {
        responseCode: Sequelize.STRING,
        status: Sequelize.STRING,
        message: Sequelize.STRING,
        tsepToken: Sequelize.STRING,
        maskedCardNumber: Sequelize.STRING,
        cardType: Sequelize.STRING,
        transactionID: Sequelize.STRING,
        expirationDate: Sequelize.STRING
    })

    var Merchant = db.define('merchant', {
        deviceID: Sequelize.STRING,
        transactionKey: Sequelize.STRING,
        merchantID: Sequelize.STRING,
        developerID: Sequelize.STRING,
        uiDomain: Sequelize.TEXT,
        apiDomain: Sequelize.TEXT
    })

    var AgentStateHistory = db.define('agentstatehistory', {
        state: Sequelize.STRING,
        timeInState: Sequelize.INTEGER,
        date: Sequelize.DATE
    })

    var Suppression = db.define('suppression', {
        startDate: Sequelize.DATE,
        actualStartDate: Sequelize.DATE,
        endDate: Sequelize.DATE,
        actualEndDate: Sequelize.DATE,
        finished: {
            type: Sequelize.BOOLEAN,
            defaultValue: false
        },
        skipped: {
            type: Sequelize.BOOLEAN,
            defaultValue: false
        },
        currentStage: Sequelize.BOOLEAN
    })

    var SuppressionHistory = db.define('suppressionhistory', {
        file: Sequelize.STRING,
        filename: Sequelize.STRING,
        stage: Sequelize.INTEGER,
        updateOnly: Sequelize.BOOLEAN,
        total: Sequelize.INTEGER,
        created: Sequelize.INTEGER,
        updated: Sequelize.INTEGER,
        ignored: Sequelize.INTEGER,
        invalid: Sequelize.INTEGER
    })

    var BatchChangeHistory = db.define('batchchangehistory', {
        file: Sequelize.STRING,
        filename: Sequelize.STRING,
        type: Sequelize.STRING,
        stage: Sequelize.INTEGER,
        total: Sequelize.INTEGER,
        created: Sequelize.INTEGER,
        updated: Sequelize.INTEGER,
        ignored: Sequelize.INTEGER,
        invalid: Sequelize.INTEGER
    })

    var RecurringPayment = db.define('recurringpayment', {
        amount: Sequelize.FLOAT,
        every: Sequelize.INTEGER,
        unit: Sequelize.STRING, //week, month, quarter
        firstPayment: Sequelize.DATE,
        completed: Sequelize.INTEGER, //how many payments have been processed
        isCancelled: Sequelize.BOOLEAN,
        isFinished: Sequelize.BOOLEAN,
        cancelledDate: Sequelize.DATE,
        finishedDate: Sequelize.DATE,
        data: Sequelize.TEXT //json blob of the extra settings we may need?
    })

    RecurringPayment.belongsTo(Lead)
    RecurringPayment.belongsTo(Campaign)
    RecurringPayment.belongsTo(Client)
    RecurringPayment.belongsTo(CallResult)
    RecurringPayment.belongsTo(Invoice)
    RecurringPayment.hasMany(PaymentLog)

    BatchChangeHistory.belongsTo(Campaign)
    BatchChangeHistory.belongsTo(User)

    Suppression.belongsTo(Lead)
    Suppression.belongsTo(Campaign)
    Suppression.belongsTo(CampaignStage)
    Suppression.belongsTo(SuppressionHistory)

    SuppressionHistory.belongsTo(Campaign)

    // Define associations
    AgentStateHistory.belongsTo(Agent)

    Skill.belongsToMany(CampaignType, {
        through: 'campaigntypeskills'
    })

    SubSkill.hasMany(CampaignStageDateTimeRule)
    SubSkill.hasMany(BroadcastMessage)
    SubSkill.hasMany(CampaignProjections)

    CampaignType.belongsToMany(Skill, {
        through: 'campaigntypeskills'
    })

    Client.hasMany(Campaign)
    Client.hasMany(Lead)
    Client.hasMany(CallResult)
    Client.hasMany(User)
    Client.hasMany(Invoice)
    Client.hasMany(LeadImportHistory)
    Client.hasMany(ClientCosting)
    Client.hasMany(CampaignNote)
    Client.hasMany(AuditEvent)
    Client.hasOne(Merchant)

    Merchant.belongsTo(Client)

    CampaignProduct.belongsTo(Campaign)

    Campaign.belongsTo(Client)
    Campaign.belongsTo(CampaignType)
    Campaign.hasMany(CampaignTrainingDoc)
    Campaign.hasMany(BroadcastMessage)
    Campaign.belongsToMany(Lead, {
        through: CampaignLead
    })
    Campaign.hasMany(CampaignStage)
    Campaign.belongsTo(User, {
        as: 'owningUser'
    })
    Campaign.hasMany(CallResult)
    Campaign.hasMany(CallAttempt)
    Campaign.hasMany(CampaignProjections)
    Campaign.hasMany(Payroll)
    Campaign.hasMany(LoginDuration)
    Campaign.hasMany(CampaignAgentTarget)
    Campaign.hasMany(CallRecord)
    Campaign.hasMany(Invoice)
    Campaign.hasMany(CampaignProduct)
    Campaign.hasMany(LeadImportHistory)
    Campaign.hasMany(ClientCosting)
    Campaign.hasMany(CampaignNote)
    Campaign.hasMany(CampaignGoals)
    Campaign.hasMany(AuditEvent)
    Campaign.hasMany(CampaignLeadUpdate)
    Campaign.hasMany(CampaignUpdate)
    Campaign.hasMany(Suppression)

    CampaignUpdate.belongsTo(User)

    CampaignLeadUpdate.belongsTo(Campaign)

    CampaignProjections.belongsTo(Campaign)
    CampaignProjections.belongsTo(SubSkill)

    CampaignLead.belongsTo(CampaignStage, {
        as: 'currentCampaignStage',
        foreignKey: 'currentCampaignStageId'
    })
    CampaignLead.belongsTo(Lead)
    CampaignLead.belongsTo(Campaign)
    CampaignLead.hasMany(AuditEvent)

    CampaignStage.belongsTo(Campaign)
    CampaignStage.belongsToMany(Agent, {
        through: CampaignStageAgent
    })
    CampaignStage.belongsToMany(Disposition, {
        through: CampaignStageDisposition
    })
    CampaignStage.hasMany(CallAttempt)
    CampaignStage.hasMany(CallResult)
    CampaignStage.hasMany(CallRecord)
    CampaignStage.hasMany(BroadcastMessage)
    CampaignStage.hasMany(CampaignStageDateTimeRule)
    CampaignStage.hasMany(AuditEvent)
    CampaignStage.hasMany(Suppression)

    CampaignStageDisposition.belongsTo(Disposition)
    CampaignStageDisposition.belongsTo(CampaignStage)

    CampaignStageDateTimeRule.belongsTo(SubSkill)
    CampaignStageDateTimeRule.belongsTo(DateTimeRuleSet)
    CampaignStageDateTimeRule.belongsTo(CampaignStage)

    DateTimeRuleSet.hasMany(CampaignStageDateTimeRule)

    Agent.hasMany(CallResult)
    Agent.belongsTo(AgentState, {
        as: 'defaultAgentState'
    })
    Agent.belongsToMany(CampaignStage, {
        through: CampaignStageAgent
    })
    Agent.hasMany(AgentEvent)
    Agent.hasMany(BroadcastMessage)
    Agent.hasMany(Payroll)
    Agent.hasMany(LoginDuration)
    Agent.hasOne(User)
    Agent.hasMany(CallRecord)
    Agent.belongsTo(Device)
    Agent.belongsTo(Client)
    Agent.hasMany(CampaignAgentTarget)
    Agent.hasMany(AuditEvent)

    Disposition.belongsToMany(CampaignStage, {
        through: CampaignStageDisposition
    })
    Disposition.belongsToMany(CallResultField, {
        through: 'dispositioncallresultfields'
    })

    CampaignAgentTarget.belongsTo(Agent)
    CampaignAgentTarget.belongsTo(Campaign)

    CallResultFieldGroup.hasMany(CallResultField)

    CallResultFieldType.hasMany(CallResultField)

    CallResultFieldOption.belongsTo(CallResultField)

    CallResultField.hasMany(CallResultFieldValue)
    CallResultField.hasMany(CallResultFieldOption)
    CallResultField.belongsTo(CallResultFieldGroup)
    CallResultField.belongsTo(CallResultFieldType)
    CallResultField.belongsToMany(Disposition, {
        through: 'dispositioncallresultfields'
    })

    CallResultFieldValue.belongsTo(CallResultField)
    CallResultFieldValue.belongsTo(CallResult)

    Lead.belongsTo(Skill, {
        foreignKey: 'tfSkillId',
        as: 'tfSkill'
    })
    Lead.belongsTo(SubSkill, {
        foreignKey: 'tfSubSkillId',
        as: 'tfSubSkill'
    })
    Lead.belongsTo(Skill, {
        foreignKey: 'tmSkillId',
        as: 'tmSkill'
    })
    Lead.belongsTo(SubSkill, {
        foreignKey: 'tmSubSkillId',
        as: 'tmSubSkill'
    })
    Lead.hasMany(LeadFieldValue)
    Lead.belongsTo(Client)
    Lead.belongsToMany(Campaign, {
        through: CampaignLead
    })
    Lead.hasMany(CampaignLead)
    Lead.hasMany(LeadAudit)
    Lead.hasMany(CallRecord)
    Lead.hasMany(CallResult)
    Lead.hasMany(Callback)
    Lead.hasMany(Invoice)
    Lead.hasMany(AuditEvent)
    Lead.hasMany(LeadUpdate)
    Lead.hasMany(CampaignLeadUpdate)
    Lead.hasOne(CardToken)
    Lead.hasMany(PaymentLog)
    Lead.hasMany(PaymentLogHistory)
    Lead.hasMany(Suppression)
    Lead.hasMany(RecurringPayment)

    LeadAudit.belongsTo(Lead)
    LeadAudit.belongsTo(User)

    User.belongsTo(Agent)
    User.belongsTo(Client)
    User.hasMany(LeadAudit)
    User.hasMany(PanicReports)
    User.hasMany(BroadcastMessage, {
        as: 'createdUser'
    })

    BroadcastMessage.belongsTo(User, {
        as: 'createdUser'
    })
    BroadcastMessage.belongsTo(Client)

    LeadField.hasMany(LeadFieldValue)

    LeadFieldValue.belongsTo(Lead)
    LeadFieldValue.belongsTo(LeadField)

    CallAttempt.belongsTo(Lead)
    CallAttempt.belongsTo(Campaign)
    CallAttempt.belongsTo(CampaignStage)

    CallResult.belongsTo(Lead)
    CallResult.belongsTo(CampaignStage)
    CallResult.belongsTo(Agent)
    CallResult.belongsTo(Client)
    CallResult.belongsTo(Campaign)
    CallResult.hasMany(CallResultFieldValue)
    CallResult.hasMany(Sale)
    CallResult.hasMany(CallRecord)
    CallResult.hasOne(Invoice)
    CallResult.hasMany(AuditEvent)
    CallResult.hasMany(PaymentLog)
    CallResult.hasMany(RecurringPayment)

    CallRecord.belongsTo(Agent)
    CallRecord.belongsTo(Lead)
    CallRecord.belongsTo(CallResult)
    CallRecord.belongsTo(Campaign)
    CallRecord.belongsTo(CampaignStage)

    Sale.belongsTo(Campaign)
    Sale.belongsTo(Client)
    Sale.belongsTo(Agent)
    Sale.belongsTo(CampaignStage)
    Sale.belongsTo(Lead)
    Sale.belongsTo(CallResult)
    Sale.hasMany(AuditEvent)

    Callback.belongsTo(Agent)
    Callback.belongsTo(CallResult)
    Callback.belongsTo(Lead)
    Callback.belongsTo(Campaign)
    Callback.belongsTo(Client)
    Callback.hasMany(AuditEvent)

    Report.hasMany(ReportHistory)
    Report.hasMany(ReportSchedule)

    ReportSchedule.belongsTo(Report)

    ReportHistory.hasMany(ReportHistoryAudit)
    ReportHistory.belongsTo(User)
    ReportHistory.belongsTo(Report)

    ReportHistoryAudit.belongsTo(User)

    Payroll.belongsTo(Campaign)
    Payroll.belongsTo(Agent)

    LoginDuration.belongsTo(Payroll)
    LoginDuration.belongsTo(Agent)

    Device.hasOne(Agent)

    Invoice.belongsTo(CallResult)
    Invoice.belongsTo(Lead)
    Invoice.belongsTo(Campaign)
    Invoice.belongsTo(Client)
    Invoice.hasMany(InvoiceHistory)
    Invoice.hasMany(AuditEvent)
    Invoice.hasMany(PaymentLog)
    Invoice.hasOne(RecurringPayment)

    InvoiceNote.belongsTo(Invoice)
    InvoiceNote.belongsTo(User)

    InvoiceHistory.belongsTo(Invoice)

    ClientCosting.belongsTo(Campaign)

    AuditEvent.belongsTo(User, {
        foreignKey: 'owningUserId',
        as: 'owningUser'
    })

    CardPayment.belongsTo(CallResult)
    CardPayment.belongsTo(Agent)
    CardPayment.belongsTo(Lead)
    CardPayment.belongsTo(User)

    PaymentLog.belongsTo(Lead)
    PaymentLog.belongsTo(User)
    PaymentLog.belongsTo(Client)
    PaymentLog.belongsTo(Campaign)
    PaymentLog.belongsTo(Invoice)
    PaymentLog.belongsTo(CallResult)
    PaymentLog.belongsTo(RecurringPayment)
    PaymentLog.hasMany(PaymentLogHistory)

    PaymentLogHistory.belongsTo(PaymentLog)
    PaymentLogHistory.belongsTo(Lead)
    PaymentLogHistory.belongsTo(User)
    PaymentLogHistory.belongsTo(Client)
    PaymentLogHistory.belongsTo(Campaign)
    PaymentLogHistory.belongsTo(Invoice)
    PaymentLogHistory.belongsTo(CallResult)

    CardToken.belongsTo(Lead)

    UserEvent.belongsTo(User)
    UserEvent.belongsTo(Agent)

    InvoiceEvent.belongsTo(User)
    InvoiceEvent.belongsTo(Invoice)

    var Models = {
        Client: Client,
        Campaign: Campaign,
        CampaignStage: CampaignStage,
        Lead: Lead,
        Skill: Skill,
        SubSkill: SubSkill,
        CampaignType: CampaignType,
        Agent: Agent,
        CallAttempt: CallAttempt,
        CallResult: CallResult,
        CallRecord: CallRecord,
        BroadcastMessage: BroadcastMessage,
        CampaignTrainingDoc: CampaignTrainingDoc,
        DateTimeRuleSet: DateTimeRuleSet,
        Disposition: Disposition,
        AgentState: AgentState,
        LeadField: LeadField,
        LeadFieldValue: LeadFieldValue,
        Callback: Callback,
        CampaignLead: CampaignLead,
        CampaignStageAgent: CampaignStageAgent,
        CampaignStageDisposition: CampaignStageDisposition,
        CampaignStageDateTimeRule: CampaignStageDateTimeRule,
        CallResultField: CallResultField,
        CallResultFieldOption: CallResultFieldOption,
        CallResultFieldGroup: CallResultFieldGroup,
        CallResultFieldType: CallResultFieldType,
        CallResultFieldValue: CallResultFieldValue,
        CampaignProduct: CampaignProduct,
        CampaignAgentTarget: CampaignAgentTarget,
        LeadAudit: LeadAudit,
        User: User,
        AgentEvent: AgentEvent,
        Sale: Sale,
        Report: Report,
        ReportSchedule: ReportSchedule,
        ReportHistory: ReportHistory,
        ReportHistoryAudit: ReportHistoryAudit,
        CampaignProjections: CampaignProjections,
        Payroll: Payroll,
        LoginDuration: LoginDuration,
        Device: Device,
        System: System,
        Invoice: Invoice,
        InvoiceHistory: InvoiceHistory,
        LeadImportHistory: LeadImportHistory,
        ClientCosting: ClientCosting,
        CampaignNote: CampaignNote,
        EmailHistory: EmailHistory,
        CampaignGoals: CampaignGoals,
        PanicReports: PanicReports,
        RefusalReason: RefusalReason,
        AuditEvent: AuditEvent,
        LeadUpdate: LeadUpdate,
        CampaignLeadUpdate: CampaignLeadUpdate,
        CampaignUpdate: CampaignUpdate,
        CardPayment: CardPayment,
        PaymentLog: PaymentLog,
        CardToken: CardToken,
        PaymentLogHistory: PaymentLogHistory,
        Merchant: Merchant,
        AgentStateHistory: AgentStateHistory,
        Suppression: Suppression,
        SuppressionHistory: SuppressionHistory,
        BatchChangeHistory: BatchChangeHistory,
        UserEvent: UserEvent,
        InvoiceEvent,
        RecurringPayment,
        InvoiceNote
    }

    // Hooks
    require('./schemaHooks')(Models)

    // Return model definitions

    return Models
}