var _ = require('underscore')
var Sequelize = require('sequelize')
var Promise = Sequelize.Promise
var agentAPI = require('../database/redisAPI')('agent')
var campaignAPI = require('../database/redisAPI')('campaign')
var moment = require('moment')

module.exports = function (app, Models, BASE_URL, db) {
    // Get agents list
    app.get(BASE_URL + '/agents', function (req, res) {
        var where = {}
        if (req.user.isClientAgent || req.user.isClientAdmin) {
            where.clientId = req.user.clientId
        }
        Models.Agent.findAll({
            where,
            include: [Models.Device],
            order: ['name']
        })
            .then(function (items) {
                res.status(200).send(items)
            })
            .catch(function (err) {
                return res.status(500).send({
                    error: err.message
                })
            })
    })

    app.get(BASE_URL + '/agents/weeklygoals', function (req, res) {
        var query = weeklyGoalsSQL.replaceAll('{{startDate}}', req.query.startDate).replaceAll('{{endDate}}', req.query.endDate)

        if (req.user.isClientAgent || req.user.isClientAdmin) {
            var replaceStr = ' AND cr.clientId = ' + req.user.clientId
            if (req.user.isClientAgent && !req.user.isClientAdmin) {
                replaceStr += ' AND cr.agentId = ' + req.user.agentId
            }
            query = query.replaceAll('{{where}}', replaceStr)
        } else {
            query = query.replaceAll('{{where}}', ' AND a.clientId IS NULL')
        }

        db.query(query, {
            type: Sequelize.QueryTypes.SELECT
        })
            .then(function (results) {
                var rows = []
                var totals = {}
                results.forEach(row => {
                    if (row.agentId) {
                        rows.push(row)
                    } else if (row.campaignId) {
                        totals[row['Campaign Name']] = row
                    } else if (row.userId) {
                        totals[row['Supervisor']] = row
                    } else {
                        totals.grandTotal = row
                    }
                })

                var userWhere = {}
                if (req.user.isClientAgent || req.user.isClientAdmin) {
                    userWhere.clientId = req.user.clientId
                }
                Models.User.findAll({ where: userWhere }).then(function (users) {
                    var campaignWhere = {
                        clientId: {
                            $ne: 3
                        }
                    }
                    var agentWhere = {}
                    if (req.user.isClientAgent || req.user.isClientAdmin) {
                        campaignWhere.clientId = req.user.clientId
                    } else {
                        agentWhere.clientId = {
                            $eq: null
                        }
                    }
                    if (req.user.isClientAgent) {
                        agentWhere.id = req.user.agentId
                    }
                    Models.Campaign.findAll({
                        include: [{
                            model: Models.CampaignStage,
                            required: true,
                            include: [{ model: Models.Agent, where: agentWhere }]
                        }],
                        where: campaignWhere
                    }).then(function (campaigns) {
                        var agents = []
                        var promises = []
                        campaigns.forEach(function (campaign) {
                            campaign.campaignstages.forEach(function (stage) {
                                stage.agents.forEach(function (agent) {
                                    if (!_.findWhere(rows, {
                                        'Agent Name': agent.name,
                                        'Campaign Name': campaign.name
                                    })) {
                                        var found = false
                                        agents.forEach(a => {
                                            if (!found)
                                                found = (a.agent.id === agent.id && a.campaign.id === campaign.id)
                                        })
                                        if (!found) {
                                            var user = _.findWhere(users, {
                                                id: campaign.owningUserId
                                            })
                                            if (user) {
                                                agents.push({
                                                    id: agent.id,
                                                    agent: agent,
                                                    campaign: campaign,
                                                    supervisor: user
                                                })
                                            }
                                        }
                                    }

                                })
                            })
                        })

                        if (agents.length) {
                            agents.forEach(function (agent) {
                                promises.push(Models.CampaignAgentTarget.findOne({
                                    where: {
                                        campaignId: agent.campaign.id,
                                        agentId: agent.id,
                                        start: {
                                            $lt: moment(req.query.startDate).add(3, 'days').toDate()
                                        },
                                        end: {
                                            $gt: moment(req.query.startDate).add(3, 'days').toDate()
                                        }
                                    }
                                }))
                            })
                            Promise.all(promises)
                                .then(function (targets) {
                                    targets.forEach((target, i) => {
                                        var agent = agents[i]
                                        if (target && agent) {
                                            var goal = target.overrideGoal > 0 ? target.overrideGoal : target.goal
                                            var thresholdPerHour = 100
                                            if (agent.agent.hourlyRate == 11) thresholdPerHour = 60
                                            else if (agent.agent.hourlyRate == 13) thresholdPerHour = 80

                                            rows.push({
                                                'Campaign Name': agent.campaign.name,
                                                'Supervisor': agent.supervisor.name,
                                                'Agent Name': agent.agent.name,
                                                'Sch Hrs': target.scheduledHours,
                                                'Act Hours': 0,
                                                'Calls / Hr': 0,
                                                'Threshold': thresholdPerHour * target.scheduledHours,
                                                'Goal': goal,
                                                'Actual Amt': 0,
                                                'Goal %': 0,
                                                'Thresh/hr': thresholdPerHour,
                                                'Goal/hr': goal / (target.scheduledHours || 1),
                                                'Actual/hr': 0,
                                                'Acq %': 0,
                                                'Ren %': 0,
                                                'Lap %': 0,
                                                'Monday Total': 0,
                                                'Tuesday Total': 0,
                                                'Wednesday Total': 0,
                                                'Thursday Total': 0,
                                                'Friday Total': 0,
                                                'Saturday Total': 0,
                                                'Sunday Total': 0,
                                                'CC Rate': 0,
                                                'Renew Inc': 0,
                                                'Add-on': 0,
                                                'Total #': 0,
                                                'New $': 0,
                                                'New $ CC Rate': 0,
                                                'CC $': 0,
                                                'Unpaid Invoice $': 0,
                                                'Paid Invoice $': 0
                                            })
                                        }
                                    })
                                    res.status(200).send({
                                        rows: rows,
                                        totals: totals
                                    })
                                })
                                .catch(function (err) {
                                    return res.status(500).send({
                                        error: err.message
                                    })
                                })
                        } else {
                            res.status(200).send({
                                rows: rows,
                                totals: totals
                            })
                        }
                    })
                        .catch(function (err) {
                            return res.status(500).send({
                                error: err.message
                            })
                        })
                })
            })
            .catch(function (err) {
                return res.status(500).send({
                    error: err.message
                })
            })
    })

    app.get(BASE_URL + '/agents/weeklygoalsv2', function (req, res) {
        var query = weeklyGoalsV2SQL.replaceAll('{{startDate}}', req.query.startDate).replaceAll('{{endDate}}', req.query.endDate)

        if (req.user.isClientAgent || req.user.isClientAdmin) {
            var replaceStr = ' AND cr.clientId = ' + req.user.clientId
            if (req.user.isClientAgent && !req.user.isClientAdmin) {
                replaceStr += ' AND cr.agentId = ' + req.user.agentId
            }
            query = query.replaceAll('{{where}}', replaceStr)
        } else {
            query = query.replaceAll('{{where}}', ' AND a.clientId IS NULL')
        }

        db.query(query, {
            type: Sequelize.QueryTypes.SELECT
        })
            .then(function (results) {
                // for local
                //results = require('./results.js');

                var rows = []
                var totals = {}
                results.forEach(row => {
                    if (row.agentId) {
                        rows.push(row)
                    } else if (row.campaignId) {
                        totals[row['Campaign Name']] = row
                    } else if (row.userId) {
                        totals[row['Supervisor']] = row
                    } else {
                        totals.grandTotal = row
                    }
                })

                var userWhere = {}
                if (req.user.isClientAgent || req.user.isClientAdmin) {
                    userWhere.clientId = req.user.clientId
                }

                Models.User.findAll({ where: userWhere }).then(function (users) {
                    var campaignWhere = {
                        clientId: {
                            $ne: 3
                        }
                    }
                    var agentWhere = {}
                    if (req.user.isClientAgent || req.user.isClientAdmin) {
                        campaignWhere.clientId = req.user.clientId
                    } else {
                        agentWhere.clientId = {
                            $eq: null
                        }
                    }
                    if (req.user.isClientAgent) {
                        agentWhere.id = req.user.agentId
                    }
                    Models.Campaign.findAll({
                        include: [{
                            model: Models.CampaignStage,
                            required: true,
                            include: [{ model: Models.Agent, where: agentWhere }]
                        }],
                        where: campaignWhere
                    }).then(function (campaigns) {
                        var agents = []
                        var promises = []
                        campaigns.forEach(function (campaign) {
                            campaign.campaignstages.forEach(function (stage) {
                                stage.agents.forEach(function (agent) {
                                    if (!_.findWhere(rows, {
                                        'Agent Name': agent.name,
                                        'Campaign Name': campaign.name
                                    })) {
                                        var found = false
                                        agents.forEach(a => {
                                            if (!found)
                                                found = (a.agent.id === agent.id && a.campaign.id === campaign.id)
                                        })
                                        if (!found) {
                                            var user = _.findWhere(users, {
                                                id: campaign.owningUserId
                                            })
                                            if (user) {
                                                agents.push({
                                                    id: agent.id,
                                                    agent: agent,
                                                    campaign: campaign,
                                                    supervisor: user
                                                })
                                            }
                                        }
                                    }

                                })
                            })
                        })

                        if (agents.length) {
                            agents.forEach(function (agent) {
                                promises.push(Models.CampaignAgentTarget.findOne({
                                    where: {
                                        campaignId: agent.campaign.id,
                                        agentId: agent.id,
                                        start: {
                                            $lt: moment(req.query.startDate).add(3, 'days').toDate()
                                        },
                                        end: {
                                            $gt: moment(req.query.startDate).add(3, 'days').toDate()
                                        }
                                    }
                                }))
                            })
                            Promise.all(promises)
                                .then(function (targets) {
                                    targets.forEach((target, i) => {
                                        var agent = agents[i]
                                        if (target && agent) {
                                            var goal = target.overrideGoal > 0 ? target.overrideGoal : target.goal

                                            rows.push({
                                                'Campaign Name': agent.campaign.name,
                                                'Supervisor': agent.supervisor.name,
                                                'Agent Name': agent.agent.name,
                                                'Sch Hrs': target.scheduledHours,
                                                'Act Hours': 0,
                                                'Calls / Hr': 0,
                                                'Goal': goal,
                                                'Actual Amt': 0,
                                                'Goal %': 0,
                                                'Goal/hr': goal / (target.scheduledHours || 1),
                                                'Actual/hr': 0,
                                                'Acq #': 0,
                                                'Monday Total': 0,
                                                'Tuesday Total': 0,
                                                'Wednesday Total': 0,
                                                'Thursday Total': 0,
                                                'Friday Total': 0,
                                                'Saturday Total': 0,
                                                'Sunday Total': 0,
                                                'CC Rate #': 0,
                                                'CC Rate $': 0,
                                                'Renew Inc': 0,
                                                'Add-On $': 0,
                                                'CC & Paid Inv Gift #': 0,
                                                'Paid Invoice #': 0,
                                                'Total New Gifts #': 0,
                                                'New $': 0,
                                                'Total CC $': 0,
                                                'Unpaid Invoice $': 0,
                                                'Paid Invoice $': 0
                                            })
                                        }
                                    })
                                    res.status(200).send({
                                        rows: rows,
                                        totals: totals
                                    })
                                })
                                .catch(function (err) {
                                    return res.status(500).send({
                                        error: err.message
                                    })
                                })
                        } else {
                            res.status(200).send({
                                rows: rows,
                                totals: totals
                            })
                        }
                    })
                        .catch(function (err) {
                            return res.status(500).send({
                                error: err.message
                            })
                        })
                })
            })
            .catch(function (err) {
                return res.status(500).send({
                    error: err.message
                })
            })
    })

    // Get agent by id
    app.get(BASE_URL + '/agents/:id', function (req, res) {
        Models.Agent.find({
            include: [
                Models.Device
            ],
            where: {
                id: req.params.id
            }
        })
            .then(function (result) {
                res.status(200).send(result)
            })
            .catch(function (err) {
                return res.status(500).send({
                    error: err.message
                })
            })
    })

    app.get(BASE_URL + '/agents/:id/dashboardstats', function (req, res) {
        Models.Campaign.findAll({
            where: {
                startDate: {
                    $lte: new Date()
                },
                endDate: {
                    $gte: new Date()
                }
            },
            include: [{
                model: Models.CampaignStage,
                required: true,
                include: [{
                    model: Models.Agent,
                    required: true,
                    where: {
                        id: req.params.id
                    }
                }]
            }]
        }).then(function (campaigns) {
            var promises = [];
            promises.push(db.query(agentStatsSQL, {
                replacements: [req.params.id],
                type: Sequelize.QueryTypes.SELECT
            }))

            promises.push(db.query(campaignStatsSQL, {
                replacements: [req.params.id],
                type: Sequelize.QueryTypes.SELECT
            }))

            promises.push(Models.Payroll.findAll({
                where: {
                    id: req.params.id
                },
                group: ['campaignId']
            }))

            var date = moment().format('DDMMYY')
            promises.push(agentAPI.get(req.params.id, 'payroll'))
            promises.push(agentAPI.get(req.params.id, 'payroll-' + date))
            for (var i = 0; i < campaigns.length; i++) {
                var campaign = campaigns[i];
                promises.push(campaignAPI.get(campaign.id, 'payroll'))
                promises.push(campaignAPI.get(campaign.id, 'payroll-' + date))
                promises.push(campaignAPI.get(campaign.id + '-' + req.params.id, 'payroll'))
                promises.push(campaignAPI.get(campaign.id + '-' + req.params.id, 'payroll-' + date))
            }

            Promise.all(promises)
                .then(function (results) {
                    var stats = {
                        agentCampaignStatsByDate: results[0],
                        campaignStatsByDate: results[1],
                        dailyPayrolls: results[2],
                        totalPayroll: results[3],
                        todayPayroll: results[4],
                        campaignStats: []
                    }
                    for (var i = 5; i < results.length; i += 4) {
                        var campaignPayroll = {
                            campaignId: campaigns[Math.round(i / 4) - 1].dataValues.id,
                            totalHours: results[i],
                            hoursToday: results[i + 1],
                            totalAgentHours: results[i + 2],
                            agentHoursToday: results[i + 3]
                        }
                        stats.campaignStats.push(campaignPayroll);
                    }
                    res.status(200).send(stats)
                })
                .catch(function (err) {
                    return res.status(500).send({
                        error: err.message
                    })
                })
        }).catch(function (err) {
            return res.status(500).send({
                error: err.message
            })
        })
    })

    app.get(BASE_URL + '/agents/:id/callhistory', function (req, res) {
        Models.CallResult.findAll({
            where: {
                agentId: req.params.id
            },
            include: [Models.Lead, Models.Sale, Models.CallRecord],
            order: 'createdAt DESC'
        })
            .then(function (result) {
                res.status(200).send(result)
            })
            .catch(function (err) {
                return res.status(500).send({
                    error: err.message
                })
            })
    })

    app.get(BASE_URL + '/agents/:id/callhistory/pledges', function (req, res) {
        Models.CallResult.findAll({
            where: {
                agentId: req.params.id,
                giftAmount: {
                    $gt: 0
                }
            },
            include: [Models.Lead, Models.CallRecord],
            order: 'createdAt DESC'
        })
            .then(function (results) {
                results.forEach(function (result) {
                    delete result.dataValues.creditCardNumber
                    delete result.dataValues.creditCardExpDate
                    delete result.dataValues.creditCardSecurityCode
                })
                res.status(200).send(results)
            })
            .catch(function (err) {
                return res.status(500).send({
                    error: err.message
                })
            })
    })

    app.get(BASE_URL + '/agents/:id/callhistory/sales', function (req, res) {
        Models.CallResult.findAll({
            where: {
                agentId: req.params.id,
                saleAmount: {
                    $gt: 0
                }
            },
            include: [Models.Lead, Models.Sale, Models.CallRecord],
            order: 'createdAt DESC'
        })
            .then(function (results) {
                results.forEach(function (result) {
                    delete result.dataValues.creditCardNumber
                    delete result.dataValues.creditCardExpDate
                    delete result.dataValues.creditCardSecurityCode
                })
                res.status(200).send(results)
            })
            .catch(function (err) {
                return res.status(500).send({
                    error: err.message
                })
            })
    })

    app.get(BASE_URL + '/agents/:id/callhistory/refusals', function (req, res) {
        Models.CallResult.findAll({
            where: {
                agentId: req.params.id,
                refusalReason: {
                    $ne: null
                }
            },
            include: [Models.Lead, Models.CallRecord],
            order: 'createdAt DESC'
        })
            .then(function (results) {
                res.status(200).send(results)
            })
            .catch(function (err) {
                return res.status(500).send({
                    error: err.message
                })
            })
    })

    app.get(BASE_URL + '/agents/:id/callrecords', function (req, res) {
        Models.CallRecord.findAll({
            where: {
                agentId: req.params.id
            },
            include: [Models.Lead, Models.CallResult, Models.Campaign, Models.CampaignStage, Models.Agent],
            order: 'createdAt DESC'
        })
            .then(function (result) {
                res.status(200).send(result)
            })
            .catch(function (err) {
                return res.status(500).send({
                    error: err.message
                })
            })
    })

    // Get agent callbacks by agent id
    app.get(BASE_URL + '/agents/:id/callbacks', function (req, res) {
        var queryParams = {
            agentId: req.params.id,
            deleted: {
                $or: {
                    $ne: true,
                    $eq: null
                }
            }
        }

        if (req.query && req.query.countOnly) {
            Models.Callback.count({
                where: queryParams
            })
                .then(function (result) {
                    res.status(200).send({
                        count: result
                    })
                })
                .catch(function (err) {
                    return res.status(500).send({
                        error: err.message
                    })
                })
        } else {
            if (req.query && req.query.currentOnly) {
                var now = new Date()
                queryParams.startDateTime = {
                    lt: now
                }
                queryParams.endDateTime = {
                    gt: now
                }
            } else if (req.query && req.query.todayOnly) {
                var todayStart = new Date()
                todayStart.setHours(0, 0, 0, 0)

                var todayEnd = new Date()
                todayEnd.setHours(23, 59, 59, 999)

                queryParams.startDateTime = {
                    lt: todayEnd
                }
                queryParams.startDateTime = {
                    gt: todayStart
                }
            } else if (req.query && req.query.expiredOnly) {
                var now = new Date()
                queryParams.endDateTime = {
                    lt: now
                }
            }

            Models.Callback.findAll({
                where: queryParams,
                include: [Models.Agent, Models.CallResult, Models.Lead, Models.Campaign]
            })
                .then(function (items) {
                    res.status(200).send(items)
                })
                .catch(function (err) {
                    return res.status(500).send({
                        error: err.message
                    })
                })
        }
    })

    app.get(BASE_URL + '/agents/:id/campaigns', function (req, res) {
        Models.Campaign.findAll({
            where: {
                startDate: {
                    $lte: new Date()
                },
                endDate: {
                    $gte: new Date()
                }
            },
            include: [{
                model: Models.CampaignStage,
                required: true,
                include: [{
                    model: Models.Agent,
                    required: true,
                    where: {
                        id: req.params.id
                    }
                }]
            }, {
                model: Models.CampaignAgentTarget,
                where: {
                    agentId: req.params.id
                }
            }]
        })
            .then(function (results) {
                res.status(200).send(results)
            })
            .catch(function (err) {
                return res.status(500).send({
                    error: err.message
                })
            })
    })

    app.get(BASE_URL + '/agents/:id/campaigns/:campaignId', function (req, res) {
        Models.Campaign.findOne({
            where: {
                id: req.params.campaignId
            },
            include: [{
                model: Models.CampaignStage,
                required: true,
                include: [{
                    model: Models.Agent,
                    required: true,
                    where: {
                        id: req.params.id
                    },
                    include: []
                }]
            }, {
                model: Models.CampaignAgentTarget,
                where: {
                    agentId: req.params.id
                },
                required: false
            }]
        })
            .then(function (results) {
                res.status(200).send(results)
            })
            .catch(function (err) {
                return res.status(500).send({
                    error: err.message
                })
            })
    })

    // Get agent call count for today by agent id
    app.get(BASE_URL + '/agents/:id/callcounttoday', function (req, res) {
        var today = new Date()
        today.setHours(0, 0, 0, 0)

        Models.CallResult
            .count({
                where: {
                    agentId: req.params.id,
                    createdAt: {
                        gt: today
                    }
                }
            })
            .then(function (stat) {
                res.status(200).send({
                    result: (stat || 0)
                })
            })
            .catch(function (err) {
                return res.status(500).send({
                    error: err.message
                })
            })
    })

    // Get average call duration for today by agent id
    app.get(BASE_URL + '/agents/:id/avgdurationtoday', function (req, res) {
        var today = new Date()
        today.setHours(0)
        today.setMinutes(0)
        today.setSeconds(0)

        Models.CallResult
            .aggregate('duration', 'avg', {
                where: {
                    agentId: req.params.id,
                    createdAt: {
                        gt: today
                    }
                }
            })
            .then(function (stat) {
                res.status(200).send({
                    result: (stat || 0)
                })
            })
            .catch(function (err) {
                return res.status(500).send({
                    error: err.message
                })
            })
    })

    // Get total gift donation amount for today by agent id
    app.get(BASE_URL + '/agents/:id/totalgiftamounttoday', function (req, res) {
        var today = new Date()
        today.setHours(0)
        today.setMinutes(0)
        today.setSeconds(0)

        Models.CallResult
            .aggregate('grandTotal', 'sum', {
                where: {
                    agentId: req.params.id,
                    createdAt: {
                        gt: today
                    },
                    grandTotal: {
                        $ne: null
                    }
                }
            })
            .then(function (stat) {
                res.status(200).send({
                    result: (stat || 0)
                })
            })
            .catch(function (err) {
                return res.status(500).send({
                    error: err.message
                })
            })
    })

    // Get average wrap up duration for today by agent id
    app.get(BASE_URL + '/agents/:id/avgwrapupdurationtoday', function (req, res) {
        var today = new Date()
        today.setHours(0)
        today.setMinutes(0)
        today.setSeconds(0)

        Models.CallResult
            .aggregate('wrapupduration', 'avg', {
                where: {
                    agentId: req.params.id,
                    createdAt: {
                        gt: today
                    }
                }
            })
            .then(function (stat) {
                res.status(200).send({
                    result: (stat || 0)
                })
            })
            .catch(function (err) {
                return res.status(500).send({
                    error: err.message
                })
            })
    })

    // Get call answered percentage for today by agent id
    app.get(BASE_URL + '/agents/:id/answeredpercenttoday', function (req, res) {
        var today = new Date()
        today.setHours(0)
        today.setMinutes(0)
        today.setSeconds(0)

        Models.CallResult
            .findAll({
                where: {
                    agentId: req.params.id,
                    createdAt: {
                        gt: today
                    }
                }
            })
            .then(function (results) {
                var answered = 0;
                var unanswered = 0;
                var total = 0;

                if (results && results.length > 0) {
                    for (var i = 0; i < results.length; i++) {
                        if (results[i].duration)
                            answered++;
                        else
                            unanswered++;

                        total++;
                    }
                }

                res.status(200).send({
                    answered: answered,
                    unanswered: unanswered,
                    total: total
                })
            })
            .catch(function (err) {
                return res.status(500).send({
                    error: err.message
                })
            })
    })

    // Get avg gift donation per call for today by agent id
    app.get(BASE_URL + '/agents/:id/avggiftpercalltoday', function (req, res) {
        var today = new Date()
        today.setHours(0)
        today.setMinutes(0)
        today.setSeconds(0)

        Models.CallResult
            .findAll({
                where: {
                    agentId: req.params.id,
                    createdAt: {
                        gt: today
                    },
                    grandTotal: {
                        $ne: null
                    }
                }
            })
            .then(function (results) {
                if (results && results.length > 0) {
                    var totalGifts = 0,
                        avgGiftAmount = 0

                    _.each(results, function (callResult) {
                        if (callResult.grandTotal)
                            totalGifts += callResult.grandTotal
                    })

                    if (totalGifts)
                        avgGiftAmount = totalGifts / results.length

                    return res.status(200).send({
                        result: avgGiftAmount
                    })
                } else
                    return res.status(200).send({
                        result: 0
                    })
            })
            .catch(function (err) {
                return res.status(500).send({
                    error: err.message
                })
            })
    })

    app.get(BASE_URL + '/agents/:id/agenteventsbyday', function (req, res) {
        var dateFilter = new Date()

        if (req.body.dateFilter) {
            dateFilter = new Date(req.body.dateFilter)
        }

        var startDate = new Date(dateFilter)
        startDate.setHours(0)
        startDate.setMinutes(0)
        startDate.setSeconds(0)

        var endDate = new Date(dateFilter)
        endDate.setHours(23)
        endDate.setMinutes(59)
        endDate.setSeconds(59)

        Models.AgentEvent
            .findAll({
                where: {
                    agentId: req.params.id,
                    createdAt: {
                        gt: startDate
                    },
                    createdAt: {
                        lt: endDate
                    }
                }
            }).
            then(function (results) {
                return res.status(200).send(results)
            })
            .catch(function (err) {
                return res.status(500).send({
                    error: err.message
                })
            })
    })

    // Create agent
    app.post(BASE_URL + '/agents', function (req, res) {
        if (req.user.isClientAgent || req.user.isClientAdmin) {
            req.body.clientId = req.user.clientId || req.body.clientId
        }
        Models.Agent.create(req.body)
            .then(function (result) {
                res.status(201).send(result)
            })
            .catch(function (err) {
                if (err.name == "SequelizeUniqueConstraintError") {
                    //duplicate error on field, pass back errors
                    return res.status(409).send({
                        errors: err.errors
                    })
                } else {
                    return res.status(500).send({
                        error: err.message
                    })
                }
            })
    })

    // Update agent
    app.put(BASE_URL + '/agents/:id', function (req, res) {
        Models.Agent.findById(req.params.id)
            .then(function (result) {
                result.updateAttributes(req.body)
                    .then(function (result) {
                        res.status(200).send(result)
                    })
                    .catch(function (err) {
                        return res.status(500).send({
                            error: err.message
                        })
                    })
            })
            .catch(function (err) {
                return res.status(500).send({
                    error: err.message
                })
            })
    })

    // Delete agent
    app.delete(BASE_URL + '/agents/:id', function (req, res) {
        Models.Agent.destroy({
            where: {
                id: req.params.id
            }
        })
            .then(function (result) {
                res.status(200).send({
                    success: true
                })
            })
            .catch(function (err) {
                return res.status(500).send({
                    error: err.message
                })
            })
    })



    var agentStatsSQL = `SELECT 
                            callresults.campaignid as 'id',
                            DATE(callresults.createdAt) AS date,
                            SUM(CASE 
                                WHEN callresults.saleAmount > 0 then 1
                                ELSE 0
                            END) as saleCount,
                            SUM(CASE 
                                WHEN callresults.giftAmount > 0 then 1
                                ELSE 0
                            END) as giftCount,
                            SUM(CASE
                                WHEN callresults.paymentType = 'Credit Card' then 1
                                ELSE 0
                            END) as creditCardCount,
                            SUM(CASE
                                WHEN callresults.saleAmount > 0 or callresults.giftAmount > 0 then 1
                                ELSE 0
                            END) as saleOrGiftCount,
                            SUM(callresults.saleAmount) AS saleAmount,
                            SUM(callresults.giftAmount) AS giftAmount,
                            AVG(callresults.saleAmount) AS avgSaleAmount,
                            AVG(callresults.giftAmount) AS avgGiftAmount,
                            AVG(callresults.wrapupduration) AS avgWrapup,
                            AVG(callrecords.totalDurationSecs) AS avgCallDuration,
                            COUNT(callrecords.id) AS callCount
                        FROM
                            callresults
                                LEFT JOIN
                            callrecords on callresults.id = callrecords.callresultId
                                WHERE callresults.agentId = ?
                        GROUP BY DATE(callresults.createdAt), callresults.campaignId`;

    var campaignStatsSQL = `SELECT 
                            callresults.campaignid as 'id',
                            SUM(CASE 
                                WHEN callresults.saleAmount > 0 then 1
                                ELSE 0
                            END) as saleCount,
                            SUM(CASE 
                                WHEN callresults.giftAmount > 0 then 1
                                ELSE 0
                            END) as giftCount,
                            SUM(callresults.saleAmount) AS saleAmount,
                            SUM(callresults.giftAmount) AS giftAmount
                        FROM
                            callresults
                        where campaignid in
                            (select distinct(c.id) from campaignstageagents csa
                            left join campaignstages cs on cs.id = csa.campaignstageid
                            left join campaigns c on c.id = cs.campaignid
                            where agentid = ?)
                        GROUP BY callresults.campaignId`;

    var weeklyGoalsSQL = `SELECT 
            u.id as 'userId',
            u.name AS 'Supervisor',
            ca.id as 'campaignId',
            ca.name AS 'Campaign Name',
            a.id as 'agentId',
            a.name AS 'Agent Name',
            (SELECT 
                    cat.scheduledHours
                FROM
                    campaignagenttargets cat
                WHERE
                    cat.agentId = a.id
                        AND cat.campaignid = ca.id
                        AND cat.start <= DATE_ADD('{{startDate}}', INTERVAL 3 DAY)
                        AND cat.end >= DATE_ADD('{{startDate}}', INTERVAL 3 DAY)
                ORDER BY id DESC
                LIMIT 1) AS 'Sch Hrs',
            (SELECT 
                    IFNULL(ROUND(SUM(duration) / 60 / 60), 0)
                FROM
                    userevents
                WHERE
                    agentId = a.id
                        AND createdAt >= '{{startDate}}'
                        AND createdAt <= '{{endDate}}')  AS 'Act Hours',
            ROUND((COUNT(cr.id) / (SELECT 
                    IFNULL(ROUND(SUM(duration) / 60 / 60), 0)
                FROM
                    userevents
                WHERE
                    agentId = a.id
                        AND createdAt >= '{{startDate}}'
                        AND createdAt <= '{{endDate}}'))) AS 'Calls / Hr',
            ROUND((SELECT 
                            IF(cat.overrideGoal > 0, cat.overrideGoal, cat.goal) as 'goal'
                        FROM
                            campaignagenttargets cat
                        WHERE
                            cat.agentId = a.id
                                AND cat.campaignid = ca.id
                                AND cat.start <= DATE_ADD('{{startDate}}', INTERVAL 3 DAY)
                                AND cat.end >= DATE_ADD('{{startDate}}', INTERVAL 3 DAY) ORDER BY id DESC limit 1)) AS 'Goal',
            ROUND(SUM(CASE
                WHEN cr.paymentType = 'Invoice' THEN (i.grandTotal - coalesce(i.amountRemaining, 0))
                ELSE cr.grandTotal
            END)) AS 'Actual Amt',
            ROUND((SUM(CASE
                WHEN cr.paymentType = 'Invoice' THEN (i.grandTotal - coalesce(i.amountRemaining, 0))
                ELSE cr.grandTotal
            END) / (SELECT 
                IF(cat.overrideGoal > 0, cat.overrideGoal, cat.goal) as 'goal'
            FROM
                campaignagenttargets cat
            WHERE
                cat.agentId = a.id
                    AND cat.campaignid = ca.id
                    AND cat.start <= DATE_ADD('{{startDate}}', INTERVAL 3 DAY)
                    AND cat.end >= DATE_ADD('{{startDate}}', INTERVAL 3 DAY) ORDER BY id DESC limit 1)) * 100) as 'Goal %',
            (IF(a.hourlyRate = 11,
                60,
                IF(a.hourlyRate = 13, 80, 100))) AS 'Thresh/hr',
            ROUND((SELECT 
                            IF(cat.overrideGoal > 0, cat.overrideGoal, cat.goal) as 'goal'
                        FROM
                            campaignagenttargets cat
                        WHERE
                            cat.agentId = a.id
                                AND cat.campaignId = ca.id
                                AND cat.start <= DATE_ADD('{{startDate}}', INTERVAL 3 DAY)
                                AND cat.end >= DATE_ADD('{{startDate}}', INTERVAL 3 DAY) ORDER BY id DESC limit 1) / (SELECT 
                            cat.scheduledHours
                        FROM
                            campaignagenttargets cat
                        WHERE
                            cat.agentId = a.id
                                AND cat.campaignid = ca.id
                                AND cat.start <= DATE_ADD('{{startDate}}', INTERVAL 3 DAY)
                                AND cat.end >= DATE_ADD('{{startDate}}', INTERVAL 3 DAY) ORDER BY id DESC limit 1)) AS 'Goal/hr',
            ROUND((100 / (SUM(cr.grandTotal))) * SUM(CASE
                WHEN
                    cr.grandTotal > 0
                        AND ((ct.name = 'Telesales' AND cr.skill <> 'Renewal') OR (ct.name = 'Telefunding' AND cr.skill = 'Acquisition'))
                THEN
                    cr.grandTotal
                ELSE 0
            END)) AS 'Acq %',
            ROUND((100 / (SUM(cr.grandTotal))) * SUM(CASE
                WHEN
                    cr.grandTotal > 0
                        AND cr.skill like 'Renewal%'
                THEN
                    cr.grandTotal
                ELSE 0
            END)) AS 'Ren %',
            ROUND((100 / (SUM(cr.grandTotal))) * SUM(CASE
                WHEN
                    cr.grandTotal > 0
                        AND cr.skill = 'Lapsed'
                THEN
                    cr.grandTotal
                ELSE 0
            END)) AS 'Lap %',
            ROUND(SUM((CASE
                        WHEN 
                            DATE(DATE_SUB(cr.createdAt, INTERVAL 5 HOUR)) = DATE('{{startDate}}') THEN cr.grandTotal
                        ELSE 0
                    END))) AS 'Monday Total',
            ROUND(SUM((CASE
                        WHEN
                            DATE(DATE_SUB(cr.createdAt, INTERVAL 5 HOUR)) = DATE_ADD(DATE('{{startDate}}'),
                                INTERVAL 1 DAY)
                        THEN
                            cr.grandTotal
                        ELSE 0
                    END))) AS 'Tuesday Total',
            ROUND(SUM((CASE
                        WHEN
                            DATE(DATE_SUB(cr.createdAt, INTERVAL 5 HOUR)) = DATE_ADD(DATE('{{startDate}}'),
                                INTERVAL 2 DAY)
                        THEN
                            cr.grandTotal
                        ELSE 0
                    END))) AS 'Wednesday Total',
            ROUND(SUM((CASE
                        WHEN
                            DATE(DATE_SUB(cr.createdAt, INTERVAL 5 HOUR)) = DATE_ADD(DATE('{{startDate}}'),
                                INTERVAL 3 DAY)
                        THEN
                            cr.grandTotal
                        ELSE 0
                    END))) AS 'Thursday Total',
            ROUND(SUM((CASE
                        WHEN
                            DATE(DATE_SUB(cr.createdAt, INTERVAL 5 HOUR)) = DATE_ADD(DATE('{{startDate}}'),
                                INTERVAL 4 DAY)
                        THEN
                            cr.grandTotal
                        ELSE 0
                    END))) AS 'Friday Total',
            ROUND(SUM((CASE
                        WHEN
                            DATE(DATE_SUB(cr.createdAt, INTERVAL 5 HOUR)) = DATE_ADD(DATE('{{startDate}}'),
                                INTERVAL 5 DAY)
                        THEN
                            cr.grandTotal
                        ELSE 0
                    END))) AS 'Saturday Total',
            ROUND(SUM((CASE
                        WHEN
                            DATE(DATE_SUB(cr.createdAt, INTERVAL 5 HOUR)) = DATE_ADD(DATE('{{startDate}}'),
                                INTERVAL 6 DAY)
                        THEN
                            cr.grandTotal
                        ELSE 0
                    END))) AS 'Sunday Total',
            ROUND(( SUM(CASE
                    WHEN
                        cr.grandTotal > 0 AND ct.name = 'Telefunding'
                            AND cr.paymentType = 'Credit Card'
                    THEN
                        1
                    ELSE 0
                END) / SUM(CASE
                    WHEN cr.grandTotal > 0 AND ct.name = 'Telefunding' THEN 1
                    ELSE 0
                END)) * 100) AS 'CC Rate #',
            ROUND(( SUM(CASE
                    WHEN
                        cr.grandTotal > 0 AND ct.name = 'Telefunding'
                            AND cr.paymentType = 'Credit Card'
                    THEN
                        cr.grandTotal
                    ELSE 0
                END) / SUM(CASE
                    WHEN cr.grandTotal > 0 AND ct.name = 'Telefunding' THEN cr.grandTotal
                    ELSE 0
                END)) * 100) AS 'CC Rate $',
            ROUND((SUM((CASE
                        WHEN cr.grandTotal > 0 and cr.skill like 'Renewal%' THEN cr.grandTotal
                        ELSE 0
                    END)) / SUM(CAST(l.lyAmount AS UNSIGNED))) * 100) AS 'Renew Inc',
            ROUND((100 / SUM(CASE
                        WHEN cr.saleAmount > 0 THEN 1
                        ELSE 0
                    END) * SUM(CASE
                        WHEN cr.saleAmount > 0 AND cr.giftAmount > 0 THEN 1
                        ELSE 0
                    END))) AS 'Addon %',
            SUM(CASE
                WHEN cr.grandTotal > 0 THEN 1
                ELSE 0
            END) AS 'Total #',
            ROUND(SUM(IF(cr.subSkill = '2nd Appeal Non-Telefunding',
                CASE
                    WHEN
                        COALESCE(l.lastGiftAmount, 0) - COALESCE(NULLIF(l.lyAmount, 0), l.lap1Amount, 0) + cr.grandTotal > 0
                    THEN
                        IF(COALESCE(l.lastGiftAmount, 0) - COALESCE(NULLIF(l.lyAmount, 0), l.lap1Amount, 0) + cr.grandTotal > cr.grandTotal,
                            cr.grandTotal,
                            COALESCE(l.lastGiftAmount, 0) - COALESCE(NULLIF(l.lyAmount, 0), l.lap1Amount, 0) + cr.grandTotal)
                    ELSE 0
                END,
                IF(cs.name = '2nd Appeal',
                    CASE
                        WHEN
                            (SELECT 
                                    grandTotal
                                FROM
                                    callresults
                                WHERE
                                    leadid = cr.leadid AND grandTotal > 0
                                        AND campaignid = cr.campaignid
                                ORDER BY createdAt DESC
                                LIMIT 1) - COALESCE(NULLIF(l.lyAmount, 0), l.lap1Amount, 0) + cr.grandTotal > 0
                        THEN
                            IF((SELECT 
                                        grandTotal
                                    FROM
                                        callresults
                                    WHERE
                                        leadid = cr.leadid AND grandTotal > 0
                                            AND campaignid = cr.campaignid
                                    ORDER BY createdAt DESC
                                    LIMIT 1) - COALESCE(NULLIF(l.lyAmount, 0), l.lap1Amount, 0) + cr.grandTotal > cr.grandTotal,
                                cr.grandTotal,
                                (SELECT 
                                        grandTotal
                                    FROM
                                        callresults
                                    WHERE
                                        leadid = cr.leadid AND grandTotal > 0
                                            AND campaignid = cr.campaignid
                                    ORDER BY createdAt DESC
                                    LIMIT 1) - COALESCE(NULLIF(l.lyAmount, 0), l.lap1Amount, 0) + cr.grandTotal)
                        ELSE 0
                    END,
                    CASE
                        WHEN cr.grandTotal > COALESCE(NULLIF(l.lyAmount, 0), l.lap1Amount, 0) THEN cr.grandTotal - COALESCE(NULLIF(l.lyAmount, 0), l.lap1Amount, 0)
                        ELSE 0
                    END)))) AS 'New $',
            ROUND(SUM(IF(cr.subSkill = '2nd Appeal Non-Telefunding',
                CASE
                    WHEN
                        COALESCE(l.lastGiftAmount, 0) - COALESCE(NULLIF(l.lyAmount, 0), l.lap1Amount, 0) + cr.grandTotal > 0
                    THEN
                        IF(COALESCE(l.lastGiftAmount, 0) - COALESCE(NULLIF(l.lyAmount, 0), l.lap1Amount, 0) + cr.grandTotal > cr.grandTotal,
                            cr.grandTotal,
                            COALESCE(l.lastGiftAmount, 0) - COALESCE(NULLIF(l.lyAmount, 0), l.lap1Amount, 0) + cr.grandTotal)
                    ELSE 0
                END,
                IF(cs.name = '2nd Appeal',
                    CASE
                        WHEN
                            (SELECT 
                                    grandTotal
                                FROM
                                    callresults
                                WHERE
                                    leadid = cr.leadid AND grandTotal > 0
                                        AND campaignid = cr.campaignid AND cr.paymentType = 'Credit Card'
                                ORDER BY createdAt DESC
                                LIMIT 1) - COALESCE(NULLIF(l.lyAmount, 0), l.lap1Amount, 0) + cr.grandTotal > 0
                        THEN
                            IF((SELECT 
                                        grandTotal
                                    FROM
                                        callresults
                                    WHERE
                                        leadid = cr.leadid AND grandTotal > 0
                                            AND campaignid = cr.campaignid AND cr.paymentType = 'Credit Card'
                                    ORDER BY createdAt DESC
                                    LIMIT 1) - COALESCE(NULLIF(l.lyAmount, 0), l.lap1Amount, 0) + cr.grandTotal > cr.grandTotal,
                                cr.grandTotal,
                                (SELECT 
                                        grandTotal
                                    FROM
                                        callresults
                                    WHERE
                                        leadid = cr.leadid AND grandTotal > 0
                                            AND campaignid = cr.campaignid  AND cr.paymentType = 'Credit Card'
                                    ORDER BY createdAt DESC
                                    LIMIT 1) - COALESCE(NULLIF(l.lyAmount, 0), l.lap1Amount, 0) + cr.grandTotal)
                        ELSE 0
                    END,
                    CASE
                        WHEN cr.grandTotal > COALESCE(NULLIF(l.lyAmount, 0), l.lap1Amount, 0) AND cr.paymentType = 'Credit Card' THEN cr.grandTotal - COALESCE(NULLIF(l.lyAmount, 0), l.lap1Amount, 0)
                        ELSE 0
                    END)))) AS 'New $ CC',
            SUM(CASE
                WHEN cr.paymentType = 'Credit Card' THEN cr.grandTotal
                ELSE 0
            END) AS 'Total CC $',
            ROUND(SUM(CASE
                WHEN cr.paymentType = 'Invoice' THEN coalesce(i.amountRemaining, 0)
                ELSE 0
            END)) AS 'Unpaid Invoice $',
            (select sum(amount) from paymentlogs pl left join callresults plcr on plcr.id = pl.callresultId
                where plcr.paymentType = 'Invoice' and pl.ispaid and pl.actualPaymentDate >= '{{startDate}}' and pl.actualPaymentDate <= '{{endDate}}' and plcr.agentid = cr.agentId and pl.campaignid = cr.campaignId) AS 'Paid Invoice $'
        FROM
            callresults cr
                LEFT JOIN
            invoices i on i.callresultId = cr.id
                LEFT JOIN
                
            agents a ON a.id = cr.agentid
                LEFT JOIN
            campaigns ca ON ca.id = cr.campaignId
                LEFT JOIN
            campaigntypes ct on ct.id = ca.campaigntypeId
                LEFT JOIN
            users u ON u.id = ca.owninguserid
                LEFT JOIN
            clients c ON c.id = cr.clientid
                LEFT JOIN
            leads l ON l.id = cr.leadid
                LEFT JOIN
            campaignstages cs on cs.id = cr.campaignstageid
        WHERE
            cr.createdAt >= '{{startDate}}'
                AND cr.createdAt <= '{{endDate}}'
                AND cr.agentId IS NOT NULL
                AND cr.clientId <> 3
                {{where}}
        GROUP BY u.id , ca.id , a.id WITH ROLLUP`;

    var weeklyGoalsV2SQL = `SELECT
        result1.*,
        result4.\`Unpaid Invoice $\`,
        result2.\`Calls / Hr\`,
        result3.\`CC & Paid Inv Gift #\`,
        result3.\`Paid Invoice #\`,
        result4.\`Total New Gifts #\`,
        result3.\`Acq #\`,
        result3.\`Renew Inc\`,
        result4.\`Add-On $\`,
        result4.\`CC Rate #\`,
        result4.\`CC Rate $\`,
        result3.\`New $\`,
        result3.\`New $ CC\`
    FROM
        (
        SELECT
            u.id as 'userId',
            u.name AS 'Supervisor',
            ca.id as 'campaignId',
            ca.name AS 'Campaign Name',
            a.id as 'agentId',
            a.name AS 'Agent Name',
            (
            SELECT
                cat.scheduledHours
            FROM
                campaignagenttargets cat
            WHERE
                cat.agentId = a.id
                AND cat.campaignid = ca.id
                AND cat.start <= DATE_ADD('{{startDate}}', INTERVAL 3 DAY)
                AND cat.end >= DATE_ADD('{{startDate}}', INTERVAL 3 DAY)
            ORDER BY
                id DESC
            LIMIT 1) AS 'Sch Hrs',
            (
            SELECT
                IFNULL(ROUND(SUM(duration) / 60 / 60), 0)
            FROM
                userevents
            WHERE
                agentId = a.id
                AND createdAt >= '{{startDate}}'
                AND createdAt <= '{{endDate}}') AS 'Act Hours',
            ROUND((SELECT
                        IF(cat.overrideGoal > 0, cat.overrideGoal, cat.goal) as 'goal'
                    FROM
                        campaignagenttargets cat
                    WHERE
                        cat.agentId = a.id
                            AND cat.campaignid = ca.id
                            AND cat.start <= DATE_ADD('{{startDate}}', INTERVAL 3 DAY)
                            AND cat.end >= DATE_ADD('{{startDate}}', INTERVAL 3 DAY) ORDER BY id DESC limit 1)) AS 'Goal',
            ROUND(SUM(pl.amount)) AS 'Actual Amt',
            ROUND((
                SUM(
                    pl.amount
                ) / (
                    SELECT
                        IF(cat.overrideGoal > 0, cat.overrideGoal, cat.goal) as 'goal'
                    FROM
                        campaignagenttargets cat
                    WHERE
                        cat.agentId = a.id
                            AND cat.campaignid = ca.id
                            AND cat.start <= DATE_ADD('{{startDate}}', INTERVAL 3 DAY)
                            AND cat.end >= DATE_ADD('{{startDate}}', INTERVAL 3 DAY) ORDER BY id DESC limit 1
            )) * 100) as 'Goal %',
            ROUND((SELECT
                        IF(cat.overrideGoal > 0, cat.overrideGoal, cat.goal) as 'goal'
                    FROM
                        campaignagenttargets cat
                    WHERE
                        cat.agentId = a.id
                            AND cat.campaignId = ca.id
                            AND cat.start <= DATE_ADD('{{startDate}}', INTERVAL 3 DAY)
                            AND cat.end >= DATE_ADD('{{startDate}}', INTERVAL 3 DAY) ORDER BY id DESC limit 1) / (SELECT
                        cat.scheduledHours
                    FROM
                        campaignagenttargets cat
                    WHERE
                        cat.agentId = a.id
                            AND cat.campaignid = ca.id
                            AND cat.start <= DATE_ADD('{{startDate}}', INTERVAL 3 DAY)
                            AND cat.end >= DATE_ADD('{{startDate}}', INTERVAL 3 DAY) ORDER BY id DESC limit 1)
            ) AS 'Goal/hr',
            ROUND(SUM((CASE
                    WHEN
                        DATE(DATE_SUB(pl.createdAt, INTERVAL 5 HOUR)) = DATE('{{startDate}}')
                    THEN
                        pl.amount
                    ELSE 0
                END))) AS 'Monday Total',
            ROUND(SUM((CASE
                    WHEN
                        DATE(DATE_SUB(pl.createdAt, INTERVAL 5 HOUR)) = DATE_ADD(DATE('{{startDate}}'),
                            INTERVAL 1 DAY)
                    THEN
                        pl.amount
                    ELSE 0
                END))) AS 'Tuesday Total',
            ROUND(SUM((CASE
                    WHEN
                        DATE(DATE_SUB(pl.createdAt, INTERVAL 5 HOUR)) = DATE_ADD(DATE('{{startDate}}'),
                            INTERVAL 2 DAY)
                    THEN
                        pl.amount
                    ELSE 0
                END))) AS 'Wednesday Total',
            ROUND(SUM((CASE
                    WHEN
                        DATE(DATE_SUB(pl.createdAt, INTERVAL 5 HOUR)) = DATE_ADD(DATE('{{startDate}}'),
                            INTERVAL 3 DAY)
                    THEN
                        pl.amount
                    ELSE 0
                END))) AS 'Thursday Total',
            ROUND(SUM((CASE
                    WHEN
                        DATE(DATE_SUB(pl.createdAt, INTERVAL 5 HOUR)) = DATE_ADD(DATE('{{startDate}}'),
                            INTERVAL 4 DAY)
                    THEN
                        pl.amount
                    ELSE 0
                END))) AS 'Friday Total',
            ROUND(SUM((CASE
                    WHEN
                        DATE(DATE_SUB(pl.createdAt, INTERVAL 5 HOUR)) = DATE_ADD(DATE('{{startDate}}'),
                            INTERVAL 5 DAY)
                    THEN
                    pl.amount
                    ELSE 0
                END))) AS 'Saturday Total',
            ROUND(SUM((CASE
                    WHEN
                        DATE(DATE_SUB(pl.createdAt, INTERVAL 5 HOUR)) = DATE_ADD(DATE('{{startDate}}'),
                            INTERVAL 6 DAY)
                    THEN
                        pl.amount
                    ELSE 0
                END))) AS 'Sunday Total',
            SUM(CASE
                WHEN pl.source = 'installment plan' THEN pl.amount
                WHEN cr.paymentType = 'Credit Card' THEN cr.grandTotal
                ELSE 0
            END) AS 'Total CC $',
            SUM(CASE
                # TODO: what about installment plans?
                WHEN i.invoiceType = 'Invoice'
                    #AND pl.ispaid
                    #AND pl.actualPaymentDate >= '{{startDate}}'
                    #AND pl.actualPaymentDate <= '{{endDate}}'
                THEN pl.amount
                ELSE 0
            END) AS 'Paid Invoice $'
        FROM
            paymentlogs pl
        LEFT JOIN
        callresults cr
        ON
            cr.id = pl.callresultId
        LEFT JOIN
        invoices i on
            i.id = pl.invoiceId
        LEFT JOIN
        agents a ON
            a.id = cr.agentid
        LEFT JOIN
        campaigns ca ON
            ca.id = pl.campaignId
        LEFT JOIN
        campaigntypes ct on
            ct.id = ca.campaigntypeId
        LEFT JOIN
        users u ON
            u.id = ca.owninguserid
        LEFT JOIN
        clients c ON
            c.id = pl.clientid
        LEFT JOIN
        leads l ON
            l.id = pl.leadid
        LEFT JOIN
        campaignstages cs on
            cs.id = cr.campaignstageid
        WHERE
            # Invoices paid with credit card mean full amount OR installment plan, in which we want all payment logs created within that timeframe
            ((
            i.invoiceType = 'Invoice'
            # updatedAt - because 2517898 case - paid through collections & updated invoice
            AND i.updatedAt >= '{{startDate}}'
            AND i.updatedAt <= '{{endDate}}'
            AND pl.createdAt IN (
                SELECT
                createdAt
                FROM
                paymentlogs pl2
                WHERE pl2.invoiceId = pl.invoiceId
                GROUP BY
                createdAt
                HAVING
                COUNT(*) > 1
            ))
            # Invoices that do NOT have more than 1 payment log created at the same time mean full payment or one-off partial
            OR (
                i.invoiceType = 'Invoice'
                AND pl.createdAt >= '{{startDate}}'
                AND pl.createdAt <= '{{endDate}}'
                AND pl.createdAt NOT IN (
                -- This subquery creates a list of all createdAt timestamps
                -- that are associated with more than one payment log.
                SELECT
                createdAt
                FROM
                paymentlogs pl3
                WHERE pl3.invoiceId = pl.invoiceId
                GROUP BY
                createdAt
                HAVING
                COUNT(*) > 1
            ))
            # Laslty, credit card invoice types capture 1 time payment OR installment plans, which we want all payment logs created within that time frame
            OR (i.invoiceType = 'Credit Card'
                AND i.createdAt >= '{{startDate}}'
                AND i.createdAt <= '{{endDate}}'))
            AND not pl.disabled
            AND not pl.deleted
            AND cr.agentId IS NOT NULL
            AND cr.clientId NOT IN (3)
            {{where}}
        GROUP BY
            u.id ,
            ca.id ,
            a.id WITH ROLLUP) as result1
    LEFT JOIN (
        SELECT
            result2_inner.*
        FROM
            (
            SELECT
                u.id as 'userId',
                ca.id as 'campaignId',
                a.id as 'agentId',
                ROUND((COUNT(cr.id) / (SELECT
                            IFNULL(ROUND(SUM(duration) / 60 / 60), 0)
                        FROM
                            userevents
                        WHERE
                            agentId = a.id
                                AND createdAt >= '{{startDate}}'
                                AND createdAt <= '{{endDate}}'))
                ) AS 'Calls / Hr'
            FROM
                callresults cr
            LEFT JOIN
            invoices i on
                i.callresultId = cr.id
            LEFT JOIN
            agents a ON
                a.id = cr.agentid
            LEFT JOIN
            campaigns ca ON
                ca.id = cr.campaignId
            LEFT JOIN
            campaigntypes ct on
                ct.id = ca.campaigntypeId
            LEFT JOIN
            users u ON
                u.id = ca.owninguserid
            LEFT JOIN
            clients c ON
                c.id = cr.clientid
            LEFT JOIN
            leads l ON
                l.id = cr.leadid
            LEFT JOIN
            campaignstages cs on
                cs.id = cr.campaignstageid
            WHERE
                cr.createdAt >= '{{startDate}}'
                AND cr.createdAt <= '{{endDate}}'
                AND cr.agentId IS NOT NULL
                AND cr.clientId NOT IN (3)
                {{where}}
            GROUP BY
                u.id ,
                ca.id ,
                a.id WITH ROLLUP
            ) as result2_inner
    ) as result2 ON
        (result1.userId = result2.userId
            OR (result1.userId IS NULL
                AND result2.userId IS NULL))
        AND (result1.campaignId = result2.campaignId
            OR (result1.campaignId IS NULL
                AND result2.campaignId IS NULL))
        AND (result1.agentId = result2.agentId
            OR (result1.agentId IS NULL
                AND result2.agentId IS NULL))
    LEFT JOIN (
        SELECT
            result3_inner.*
        FROM
            (
            SELECT
                u.id as 'userId',
                ca.id as 'campaignId',
                a.id as 'agentId',
                ROUND((SUM((CASE
                    WHEN i.grandTotal > 0 
                    AND cr.skill like 'Renewal%' 
                    AND cr.subSkill NOT LIKE '%2nd Appeal Non-Telefunding%'
                    THEN i.grandTotal
                    ELSE 0
                END)) / SUM(CAST(l.lyAmount AS UNSIGNED))) * 100) AS 'Renew Inc',
                SUM(CASE
                    # if we found an invoice then we know we have a gift - this scope ALWAYS has at least 1 payment log
                    WHEN i.grandTotal > 0 THEN 1 
                    ELSE 0
                END) AS 'CC & Paid Inv Gift #',
                SUM(CASE
                    # if we found an invoice then we know we have a gift - this scope ALWAYS has at least 1 payment log
                    WHEN i.grandTotal > 0 AND i.invoiceType = 'Invoice' THEN 1 
                    ELSE 0
                END) AS 'Paid Invoice #',
                ROUND(SUM(CASE
                    WHEN
                        ((ct.name = 'Telesales' AND cr.skill <> 'Renewal') OR (ct.name = 'Telefunding' AND cr.skill = 'Acquisition')) 
                        AND i.grandTotal > 0
                    THEN
                        1
                    ELSE 0
                END)) AS 'Acq #',
                ROUND(SUM(IF(cr.subSkill = '2nd Appeal Non-Telefunding',
                CASE
                    WHEN
                        COALESCE(l.lastGiftAmount, 0) - COALESCE(NULLIF(l.lyAmount, 0), l.lap1Amount, 0) + cr.grandTotal > 0
                    THEN
                        IF(COALESCE(l.lastGiftAmount, 0) - COALESCE(NULLIF(l.lyAmount, 0), l.lap1Amount, 0) + cr.grandTotal > cr.grandTotal,
                            cr.grandTotal,
                            COALESCE(l.lastGiftAmount, 0) - COALESCE(NULLIF(l.lyAmount, 0), l.lap1Amount, 0) + cr.grandTotal)
                    ELSE 0
                END,
                IF(cs.name = '2nd Appeal',
                    CASE
                        WHEN
                            (SELECT
                                    grandTotal
                                FROM
                                    callresults
                                WHERE
                                    leadid = cr.leadid AND grandTotal > 0
                                        AND campaignid = cr.campaignid
                                ORDER BY createdAt DESC
                                LIMIT 1) - COALESCE(NULLIF(l.lyAmount, 0), l.lap1Amount, 0) + cr.grandTotal > 0
                        THEN
                            IF((SELECT
                                        grandTotal
                                    FROM
                                        callresults
                                    WHERE
                                        leadid = cr.leadid AND grandTotal > 0
                                            AND campaignid = cr.campaignid
                                    ORDER BY createdAt DESC
                                    LIMIT 1) - COALESCE(NULLIF(l.lyAmount, 0), l.lap1Amount, 0) + cr.grandTotal > cr.grandTotal,
                                cr.grandTotal,
                                (SELECT
                                        grandTotal
                                    FROM
                                        callresults
                                    WHERE
                                        leadid = cr.leadid AND grandTotal > 0
                                            AND campaignid = cr.campaignid
                                    ORDER BY createdAt DESC
                                    LIMIT 1) - COALESCE(NULLIF(l.lyAmount, 0), l.lap1Amount, 0) + cr.grandTotal)
                        ELSE 0
                    END,
                    CASE
                        WHEN cr.grandTotal > COALESCE(NULLIF(l.lyAmount, 0), l.lap1Amount, 0) THEN cr.grandTotal - COALESCE(NULLIF(l.lyAmount, 0), l.lap1Amount, 0)
                        ELSE 0
                    END)))) AS 'New $',
                ROUND(SUM(
                IF(
                    cr.subSkill = '2nd Appeal Non-Telefunding',
                    CASE
                        WHEN
                            COALESCE(l.lastGiftAmount, 0) - COALESCE(NULLIF(l.lyAmount, 0), l.lap1Amount, 0) + cr.grandTotal > 0
                        THEN
                            IF(COALESCE(l.lastGiftAmount, 0) - COALESCE(NULLIF(l.lyAmount, 0), l.lap1Amount, 0) + cr.grandTotal > cr.grandTotal,
                                cr.grandTotal,
                                COALESCE(l.lastGiftAmount, 0) - COALESCE(NULLIF(l.lyAmount, 0), l.lap1Amount, 0) + cr.grandTotal)
                        ELSE 0
                    END,
                IF(cs.name = '2nd Appeal',
                    CASE
                        WHEN
                            (SELECT
                                    grandTotal
                                FROM
                                    callresults
                                WHERE
                                    leadid = cr.leadid AND grandTotal > 0
                                        AND campaignid = cr.campaignid AND cr.paymentType = 'Credit Card'
                                ORDER BY createdAt DESC
                                LIMIT 1) - COALESCE(NULLIF(l.lyAmount, 0), l.lap1Amount, 0) + cr.grandTotal > 0
                        THEN
                            IF((SELECT
                                        grandTotal
                                    FROM
                                        callresults
                                    WHERE
                                        leadid = cr.leadid AND grandTotal > 0
                                            AND campaignid = cr.campaignid AND cr.paymentType = 'Credit Card'
                                    ORDER BY createdAt DESC
                                    LIMIT 1) - COALESCE(NULLIF(l.lyAmount, 0), l.lap1Amount, 0) + cr.grandTotal > cr.grandTotal,
                                cr.grandTotal,
                                (SELECT
                                        grandTotal
                                    FROM
                                        callresults
                                    WHERE
                                        leadid = cr.leadid AND grandTotal > 0
                                            AND campaignid = cr.campaignid AND cr.paymentType = 'Credit Card'
                                    ORDER BY createdAt DESC
                                    LIMIT 1) - COALESCE(NULLIF(l.lyAmount, 0), l.lap1Amount, 0) + cr.grandTotal)
                        ELSE 0
                    END,
                    CASE
                        WHEN cr.grandTotal > COALESCE(NULLIF(l.lyAmount, 0), l.lap1Amount, 0) AND cr.paymentType = 'Credit Card' THEN cr.grandTotal - COALESCE(NULLIF(l.lyAmount, 0), l.lap1Amount, 0)
                        ELSE 0
                    END)))) AS 'New $ CC'
            FROM
                invoices i
            LEFT JOIN paymentlogs pl
                ON
                pl.invoiceId = i.id
                AND pl.id = (
                    SELECT
                        id
                    FROM
                        paymentlogs pl2
                    WHERE
                        pl2.invoiceId = i.id
                        # This handles payment plans, because the batch of payment plans is created at the same time, so we'd grab 1 of them,
                        # and hence we don't want to look at isPaid
                        # AND pl2.isPaid = 1
                        # TODO: but what about failures? where a new batch of payments is created later?
                        AND pl2.createdAt >= '{{startDate}}'
                        AND pl2.createdAt <= '{{endDate}}'
                        # Grab installment plan payment logs, because we handle filtering on that in the outer logic below
                        # AND pl2.source != 'installment plan'
                        AND not pl2.disabled
                        AND not pl2.deleted
                    LIMIT 1
                )
            LEFT JOIN
            callresults cr ON
                cr.id = i.callresultId
            LEFT JOIN
            agents a ON
                a.id = cr.agentid
            LEFT JOIN
            campaigns ca ON
                ca.id = i.campaignId
            LEFT JOIN
            campaigntypes ct on
                ct.id = ca.campaigntypeId
            LEFT JOIN
            users u ON
                u.id = ca.owninguserid
            LEFT JOIN
            clients c ON
                c.id = i.clientid
            LEFT JOIN
            leads l ON
                l.id = i.leadid
            LEFT JOIN
            campaignstages cs on
                cs.id = cr.campaignstageid
            WHERE
                pl.id IS NOT NULL
                -- don't fetch invoices where no payment log is found
                AND
                # When not Credit Card, we want payment log date instead of Invoice date
                # this gets partial non-cc invoice payments
                ((i.invoiceType = 'Invoice'
                    AND pl.createdAt >= '{{startDate}}'
                    AND pl.createdAt <= '{{endDate}}')
                # When Credit Card, then first payment is invoice creation date
                OR (i.invoiceType = 'Credit Card'
                    AND i.createdAt >= '{{startDate}}'
                    AND i.createdAt <= '{{endDate}}'))
                AND cr.agentId IS NOT NULL
                AND cr.clientId NOT IN (3)
                {{where}}
            GROUP BY
                u.id ,
                ca.id ,
                a.id WITH ROLLUP
            ) as result3_inner
    ) as result3 ON
        (result1.userId = result3.userId
            OR (result1.userId IS NULL
                AND result3.userId IS NULL))
        AND (result1.campaignId = result3.campaignId
            OR (result1.campaignId IS NULL
                AND result3.campaignId IS NULL))
        AND (result1.agentId = result3.agentId
            OR (result1.agentId IS NULL
                AND result3.agentId IS NULL))
    LEFT JOIN (
        SELECT
            result4_inner.*
        FROM
            (
            SELECT
                u.id as 'userId',
                ca.id as 'campaignId',
                a.id as 'agentId',
                SUM(cr.saleAmount) AS 'Add-On $',
                ROUND(SUM(CASE
                    WHEN i.invoiceType = 'Invoice' THEN coalesce(i.grandTotal, 0)
                    ELSE 0
                END)) AS 'Unpaid Invoice $',
                ROUND(( SUM(CASE
                    WHEN
                        i.grandTotal > 0 AND ct.name = 'Telefunding'
                            AND i.invoiceType = 'Credit Card'
                    THEN
                        1
                    ELSE 0
                END) / SUM(CASE
                    WHEN i.grandTotal > 0 AND ct.name = 'Telefunding' THEN 1
                    ELSE 0
                END)) * 100) AS 'CC Rate #',
                ROUND(( SUM(CASE
                    WHEN
                        i.grandTotal > 0 AND ct.name = 'Telefunding'
                            AND i.invoiceType = 'Credit Card'
                    THEN
                        i.grandTotal
                    ELSE 0
                END) / SUM(CASE
                    WHEN i.grandTotal > 0 AND ct.name = 'Telefunding' THEN i.grandTotal
                    ELSE 0
                END)) * 100) AS 'CC Rate $',
                SUM(CASE
                    # Total # CC & INV gifts CREATED THIS WEEK (paid or unpaid)
                    WHEN i.grandTotal > 0 THEN 1 
                    ELSE 0
                END) AS 'Total New Gifts #'
            FROM
                invoices i
            LEFT JOIN
                # we still have to figure out agent attribution through the call result
                callresults cr ON
                cr.id = i.callresultId
            LEFT JOIN
            agents a ON
                a.id = cr.agentid
            LEFT JOIN
            campaigns ca ON
                ca.id = i.campaignId
            LEFT JOIN
            campaigntypes ct on
                ct.id = ca.campaigntypeId
            LEFT JOIN
            users u ON
                u.id = ca.owninguserid
            LEFT JOIN
            clients c ON
                c.id = i.clientid
            LEFT JOIN
            leads l ON
                l.id = i.leadid
            LEFT JOIN
            campaignstages cs on
                cs.id = cr.campaignstageid
            WHERE
                i.createdAt >= '{{startDate}}'
                AND i.createdAt <= '{{endDate}}'
                AND cr.agentId IS NOT NULL
                AND cr.clientId NOT IN (3)
                {{where}}
            GROUP BY
                u.id ,
                ca.id ,
                a.id WITH ROLLUP
            ) as result4_inner
    ) as result4 ON
        (result1.userId = result4.userId
            OR (result1.userId IS NULL
                AND result4.userId IS NULL))
        AND (result1.campaignId = result4.campaignId
            OR (result1.campaignId IS NULL
                AND result4.campaignId IS NULL))
        AND (result1.agentId = result4.agentId
            OR (result1.agentId IS NULL
                AND result4.agentId IS NULL));
    `;
}

String.prototype.replaceAll = function (search, replacement) {
    var target = this;
    return target.replace(new RegExp(search, 'g'), replacement);
}